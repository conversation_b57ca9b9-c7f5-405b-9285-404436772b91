# -*- coding: utf-8 -*-
"""
对比分析服务
"""
import json
from datetime import datetime
from flask import current_app
from difflib import SequenceMatcher

class ComparisonService:
    """对比分析服务类"""
    
    def __init__(self):
        pass
    
    def calculate_similarity(self, str1, str2):
        """计算两个字符串的相似度"""
        if not str1 or not str2:
            return 0.0
        
        return SequenceMatcher(None, str(str1).lower(), str(str2).lower()).ratio()
    
    def normalize_value(self, value):
        """标准化值"""
        if value is None:
            return ""

        # 转换为字符串并去除空白
        normalized = str(value).strip()

        # 将斜杠视为空值（常用于表示"无"或"空"）
        if normalized == "/" or normalized == "":
            return ""

        # 处理数字格式
        if normalized.replace('.', '').replace('-', '').replace(',', '').isdigit():
            try:
                # 尝试转换为浮点数再转回字符串，统一数字格式
                num_value = float(normalized.replace(',', ''))
                if num_value.is_integer():
                    normalized = str(int(num_value))
                else:
                    normalized = f"{num_value:.2f}"
            except ValueError:
                pass

        return normalized.lower()
    
    def compare_field_values(self, ai_value, system_value, field_name, analysis_type):
        """比较字段值"""
        try:
            # 标准化值
            ai_normalized = self.normalize_value(ai_value)
            system_normalized = self.normalize_value(system_value)
            
            # 如果两个值都为空，认为匹配
            if not ai_normalized and not system_normalized:
                return {
                    'field_name': field_name,
                    'ai_value': ai_value,
                    'system_value': system_value,
                    'match': True,
                    'similarity': 1.0,
                    'match_type': 'both_empty'
                }
            
            # 如果其中一个为空，认为不匹配
            if not ai_normalized or not system_normalized:
                return {
                    'field_name': field_name,
                    'ai_value': ai_value,
                    'system_value': system_value,
                    'match': False,
                    'similarity': 0.0,
                    'match_type': 'one_empty'
                }
            
            # 计算相似度
            similarity = self.calculate_similarity(ai_normalized, system_normalized)
            
            # 根据字段类型和分析类型设置匹配阈值
            threshold = self.get_match_threshold(field_name, analysis_type)
            
            # 判断是否匹配
            is_match = similarity >= threshold
            
            # 确定匹配类型
            if similarity == 1.0:
                match_type = 'exact_match'
            elif is_match:
                match_type = 'fuzzy_match'
            else:
                match_type = 'no_match'
            
            return {
                'field_name': field_name,
                'ai_value': ai_value,
                'system_value': system_value,
                'match': is_match,
                'similarity': similarity,
                'match_type': match_type,
                'threshold': threshold
            }
            
        except Exception as e:
            current_app.logger.error(f"字段值比较失败: {str(e)}")
            return {
                'field_name': field_name,
                'ai_value': ai_value,
                'system_value': system_value,
                'match': False,
                'similarity': 0.0,
                'match_type': 'error',
                'error': str(e)
            }
    
    def get_match_threshold(self, field_name, analysis_type):
        """获取字段匹配阈值"""
        # 默认阈值
        default_threshold = 0.8
        
        # 特殊字段的阈值设置
        field_thresholds = {
            # 精确匹配字段
            'account_number': 1.0,
            'product_code': 1.0,
            'trade_id': 1.0,
            'member_code': 1.0,
            'contract_code': 1.0,
            
            # 数值字段
            'amount': 0.95,
            'balance': 0.95,
            'price': 0.95,
            'rate': 0.95,
            'fee': 0.95,
            
            # 日期字段
            'date': 0.9,
            'effective_date': 0.9,
            'maturity_date': 0.9,
            'trade_date': 0.9,
            
            # 名称字段
            'name': 0.7,
            'customer_name': 0.7,
            'product_name': 0.7,
            'member_name': 0.7,
            'broker_name': 0.7,
            
            # 描述字段
            'description': 0.6,
            'remark': 0.6,
            'note': 0.6
        }
        
        # 根据分析类型调整阈值
        type_adjustments = {
            'future': 0.0,  # 期货分析要求较高精度
            'financial': -0.05,  # 理财产品可以稍微宽松
            'broker_interest': 0.0,
            'futures_member': 0.0,
            'ningyin_fee': -0.05,
            'non_standard_trade': 0.0
        }
        
        base_threshold = field_thresholds.get(field_name.lower(), default_threshold)
        adjustment = type_adjustments.get(analysis_type, 0.0)
        
        return max(0.5, min(1.0, base_threshold + adjustment))
    
    def extract_comparable_fields(self, ai_result, system_result, analysis_type):
        """提取可比较的字段"""
        try:
            comparable_fields = {}
            
            # 从AI结果中提取字段
            ai_fields = {}
            if isinstance(ai_result, dict):
                extracted_fields = ai_result.get('extracted_fields', {})
                if isinstance(extracted_fields, dict):
                    ai_fields = extracted_fields
            
            # 从系统结果中提取字段
            system_fields = {}
            if isinstance(system_result, dict) and 'results' in system_result:
                for result in system_result['results']:
                    if result.get('found') and 'data' in result:
                        data = result['data']
                        if isinstance(data, dict):
                            # 根据分析类型提取相应的字段
                            if analysis_type == 'future' and 'account_info' in data:
                                system_fields.update(data['account_info'])
                            elif analysis_type == 'financial' and 'product_info' in data:
                                system_fields.update(data['product_info'])
                            elif analysis_type == 'broker_interest' and 'interest_info' in data:
                                system_fields.update(data['interest_info'])
                            elif analysis_type == 'futures_member' and 'member_info' in data:
                                system_fields.update(data['member_info'])
                            elif analysis_type == 'ningyin_fee' and 'fee_info' in data:
                                system_fields.update(data['fee_info'])
                            elif analysis_type == 'non_standard_trade' and 'trade_info' in data:
                                system_fields.update(data['trade_info'])
                            else:
                                system_fields.update(data)
            
            # 找出共同的字段
            ai_keys = set(ai_fields.keys())
            system_keys = set(system_fields.keys())
            common_keys = ai_keys.intersection(system_keys)
            
            # 构建可比较字段字典
            for key in common_keys:
                comparable_fields[key] = {
                    'ai_value': ai_fields[key],
                    'system_value': system_fields[key]
                }
            
            # 添加只在AI结果中存在的字段
            ai_only_keys = ai_keys - system_keys
            for key in ai_only_keys:
                comparable_fields[key] = {
                    'ai_value': ai_fields[key],
                    'system_value': None
                }
            
            # 添加只在系统结果中存在的字段
            system_only_keys = system_keys - ai_keys
            for key in system_only_keys:
                comparable_fields[key] = {
                    'ai_value': None,
                    'system_value': system_fields[key]
                }
            
            return comparable_fields
            
        except Exception as e:
            current_app.logger.error(f"提取可比较字段失败: {str(e)}")
            return {}
    
    def compare_results(self, ai_result, system_result, analysis_type):
        """比较AI结果和系统结果"""
        try:
            # 提取可比较的字段
            comparable_fields = self.extract_comparable_fields(ai_result, system_result, analysis_type)
            
            if not comparable_fields:
                return {
                    'success': True,
                    'total_fields': 0,
                    'matched_fields': 0,
                    'accuracy_score': 0.0,
                    'field_comparisons': [],
                    'summary': {
                        'exact_matches': 0,
                        'fuzzy_matches': 0,
                        'no_matches': 0,
                        'ai_only_fields': 0,
                        'system_only_fields': 0
                    },
                    'message': '没有找到可比较的字段'
                }
            
            # 逐个比较字段
            field_comparisons = []
            summary = {
                'exact_matches': 0,
                'fuzzy_matches': 0,
                'no_matches': 0,
                'ai_only_fields': 0,
                'system_only_fields': 0
            }
            
            for field_name, values in comparable_fields.items():
                comparison = self.compare_field_values(
                    values['ai_value'],
                    values['system_value'],
                    field_name,
                    analysis_type
                )
                
                field_comparisons.append(comparison)
                
                # 更新统计
                match_type = comparison.get('match_type', 'no_match')
                if match_type == 'exact_match':
                    summary['exact_matches'] += 1
                elif match_type == 'fuzzy_match':
                    summary['fuzzy_matches'] += 1
                elif match_type == 'one_empty':
                    if comparison['ai_value'] is None:
                        summary['system_only_fields'] += 1
                    else:
                        summary['ai_only_fields'] += 1
                else:
                    summary['no_matches'] += 1
            
            # 计算准确率
            total_fields = len(field_comparisons)
            matched_fields = summary['exact_matches'] + summary['fuzzy_matches']
            accuracy_score = matched_fields / total_fields if total_fields > 0 else 0.0
            
            # 计算加权准确率（考虑相似度）
            total_similarity = sum(comp.get('similarity', 0.0) for comp in field_comparisons)
            weighted_accuracy = total_similarity / total_fields if total_fields > 0 else 0.0
            
            return {
                'success': True,
                'total_fields': total_fields,
                'matched_fields': matched_fields,
                'accuracy_score': accuracy_score,
                'weighted_accuracy': weighted_accuracy,
                'field_comparisons': field_comparisons,
                'summary': summary,
                'comparison_time': datetime.now().isoformat(),
                'analysis_type': analysis_type
            }
            
        except Exception as e:
            current_app.logger.error(f"结果比较失败: {str(e)}")
            return {
                'success': False,
                'error': f'结果比较失败: {str(e)}'
            }
    
    def generate_comparison_report(self, comparison_result):
        """生成对比报告"""
        try:
            if not comparison_result.get('success'):
                return {
                    'success': False,
                    'error': '对比结果无效'
                }
            
            summary = comparison_result.get('summary', {})
            total_fields = comparison_result.get('total_fields', 0)
            accuracy_score = comparison_result.get('accuracy_score', 0.0)
            
            # 生成报告文本
            report_lines = [
                f"对比分析报告",
                f"=" * 50,
                f"分析时间: {comparison_result.get('comparison_time', 'N/A')}",
                f"分析类型: {comparison_result.get('analysis_type', 'N/A')}",
                f"",
                f"总体统计:",
                f"  总字段数: {total_fields}",
                f"  匹配字段数: {comparison_result.get('matched_fields', 0)}",
                f"  准确率: {accuracy_score:.2%}",
                f"  加权准确率: {comparison_result.get('weighted_accuracy', 0.0):.2%}",
                f"",
                f"详细统计:",
                f"  完全匹配: {summary.get('exact_matches', 0)}",
                f"  模糊匹配: {summary.get('fuzzy_matches', 0)}",
                f"  不匹配: {summary.get('no_matches', 0)}",
                f"  仅AI识别: {summary.get('ai_only_fields', 0)}",
                f"  仅系统存在: {summary.get('system_only_fields', 0)}",
                f""
            ]
            
            # 添加字段详情
            field_comparisons = comparison_result.get('field_comparisons', [])
            if field_comparisons:
                report_lines.append("字段对比详情:")
                report_lines.append("-" * 30)
                
                for comp in field_comparisons:
                    field_name = comp.get('field_name', 'N/A')
                    ai_value = comp.get('ai_value', 'N/A')
                    system_value = comp.get('system_value', 'N/A')
                    similarity = comp.get('similarity', 0.0)
                    match_type = comp.get('match_type', 'unknown')
                    
                    status_icon = "✓" if comp.get('match', False) else "✗"
                    
                    report_lines.extend([
                        f"{status_icon} {field_name}:",
                        f"    AI识别值: {ai_value}",
                        f"    系统值: {system_value}",
                        f"    相似度: {similarity:.2%}",
                        f"    匹配类型: {match_type}",
                        ""
                    ])
            
            report_text = "\n".join(report_lines)
            
            return {
                'success': True,
                'report_text': report_text,
                'report_data': comparison_result
            }
            
        except Exception as e:
            current_app.logger.error(f"生成对比报告失败: {str(e)}")
            return {
                'success': False,
                'error': f'生成对比报告失败: {str(e)}'
            }
