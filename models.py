# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统 - 数据模型
"""
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True, comment='主键')
    username = db.Column(db.String(64), unique=True, nullable=False, comment='用户名')
    password_hash = db.Column(db.String(128), nullable=False, comment='密码哈希')
    role = db.Column(db.String(20), default='user', comment='角色: user, analyst, admin')
    status = db.Column(db.String(20), default='active', comment='状态: active, inactive, suspended')
    email = db.Column(db.String(255), comment='邮箱')
    phone = db.Column(db.String(20), comment='手机号')
    last_login = db.Column(db.DateTime, comment='最后登录时间')
    login_count = db.Column(db.Integer, default=0, comment='登录次数')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建者ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        """检查权限"""
        # 使用与 auth_utils.py 一致的权限定义
        role_permissions = {
            'user': [
                'upload', 'view_own_records', 'view_dashboard', 'edit_records'
            ],
            'analyst': [
                'upload', 'view_own_records', 'view_all_records',
                'tag_files', 'reanalyze', 'view_dashboard',
                'export_data', 'manage_tags', 'edit_records'
            ],
            'admin': [
                'all_permissions'  # 管理员拥有所有权限
            ]
        }

        if self.role == 'admin':
            return True

        return permission in role_permissions.get(self.role, [])
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'status': self.status,
            'email': self.email,
            'phone': self.phone,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_count': self.login_count,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class AnalysisRecord(db.Model):
    """分析记录表"""
    __tablename__ = 'analysis_records'
    
    id = db.Column(db.Integer, primary_key=True, comment='主键')
    filename = db.Column(db.String(255), nullable=False, comment='文件名')
    analysis_type = db.Column(db.String(50), nullable=False, comment='分析类型')
    ai_result = db.Column(db.Text, comment='AI识别结果(JSON)')
    expected_result = db.Column(db.Text, comment='预期正确结果(JSON)')
    system_result = db.Column(db.Text, comment='系统查询结果(JSON)')
    comparison_result = db.Column(db.Text, comment='对比结果(JSON)')
    field_accuracy = db.Column(db.Text, comment='字段级别准确率(JSON)')
    is_first_analysis = db.Column(db.Boolean, default=True, comment='是否首次分析')
    status = db.Column(db.String(20), default='pending', comment='状态: pending, processing, completed, failed')
    file_status = db.Column(db.Enum('active', 'inactive', 'deprecated', 'archived'), default='active', comment='文件状态')
    status_changed_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='状态修改人ID')
    status_changed_at = db.Column(db.DateTime, comment='状态修改时间')
    status_reason = db.Column(db.Text, comment='状态修改原因')
    file_hash = db.Column(db.String(64), comment='文件哈希值(用于去重)')
    file_info = db.Column(db.JSON, comment='文件详细信息')
    review_status = db.Column(db.Enum('pending', 'approved', 'rejected', 'needs_revision'), default='pending', comment='复核状态')
    review_priority = db.Column(db.Enum('low', 'normal', 'high'), default='normal', comment='复核优先级')
    accuracy_score = db.Column(db.Numeric(5, 4), comment='准确率评分')
    audit_status = db.Column(db.String(20), comment='审核状态: pass, fail, pending')
    audit_comment = db.Column(db.Text, comment='审核备注')
    audited_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='审核人ID')
    audited_at = db.Column(db.DateTime, comment='审核时间')
    review_comment = db.Column(db.Text, comment='复核备注')
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='复核人ID')
    reviewed_at = db.Column(db.DateTime, comment='复核时间')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_records')
    auditor = db.relationship('User', foreign_keys=[audited_by], backref='audited_records')
    reviewer = db.relationship('User', foreign_keys=[reviewed_by], backref='reviewed_records')
    status_changer = db.relationship('User', foreign_keys=[status_changed_by], backref='status_changed_records')
    
    def get_ai_result(self):
        """获取AI结果"""
        if self.ai_result:
            try:
                return json.loads(self.ai_result)
            except:
                return {}
        return {}
    
    def set_ai_result(self, result):
        """设置AI结果"""
        self.ai_result = json.dumps(result, ensure_ascii=False)
    
    def get_system_result(self):
        """获取系统结果"""
        if self.system_result:
            try:
                return json.loads(self.system_result)
            except:
                return {}
        return {}
    
    def set_system_result(self, result):
        """设置系统结果"""
        self.system_result = json.dumps(result, ensure_ascii=False)

    def get_expected_result(self):
        """获取预期结果"""
        if self.expected_result:
            try:
                return json.loads(self.expected_result)
            except:
                return {}
        return {}

    def set_expected_result(self, result):
        """设置预期结果"""
        self.expected_result = json.dumps(result, ensure_ascii=False)

    def get_comparison_result(self):
        """获取对比结果"""
        if self.comparison_result:
            try:
                return json.loads(self.comparison_result)
            except:
                return {}
        return {}

    def set_comparison_result(self, result):
        """设置对比结果"""
        self.comparison_result = json.dumps(result, ensure_ascii=False)

    def get_field_accuracy(self):
        """获取字段准确率"""
        if self.field_accuracy:
            try:
                return json.loads(self.field_accuracy)
            except:
                return {}
        return {}

    def set_field_accuracy(self, accuracy):
        """设置字段准确率"""
        self.field_accuracy = json.dumps(accuracy, ensure_ascii=False)

    def calculate_field_accuracy(self):
        """计算字段级别准确率"""
        ai_result = self.get_ai_result()
        expected_result = self.get_expected_result()

        if not ai_result or not expected_result:
            return {}

        def compare_values(ai_val, expected_val, path=""):
            """递归比较两个值"""
            if type(ai_val) != type(expected_val):
                return {"match": False, "ai_value": ai_val, "expected_value": expected_val, "path": path}

            if isinstance(ai_val, dict) and isinstance(expected_val, dict):
                result = {"match": True, "path": path, "children": {}}
                all_keys = set(ai_val.keys()) | set(expected_val.keys())

                for key in all_keys:
                    ai_sub = ai_val.get(key, "")
                    expected_sub = expected_val.get(key, "")
                    child_path = f"{path}.{key}" if path else key
                    child_result = compare_values(ai_sub, expected_sub, child_path)
                    result["children"][key] = child_result
                    if not child_result["match"]:
                        result["match"] = False

                return result

            elif isinstance(ai_val, list) and isinstance(expected_val, list):
                result = {"match": True, "path": path, "children": []}
                max_len = max(len(ai_val), len(expected_val))

                for i in range(max_len):
                    ai_item = ai_val[i] if i < len(ai_val) else None
                    expected_item = expected_val[i] if i < len(expected_val) else None
                    child_path = f"{path}[{i}]" if path else f"[{i}]"
                    child_result = compare_values(ai_item, expected_item, child_path)
                    result["children"].append(child_result)
                    if not child_result["match"]:
                        result["match"] = False

                return result

            else:
                # 简单值比较 - 将斜杠视为空值
                def normalize_for_comparison(val):
                    """标准化值用于比较，将斜杠视为空值"""
                    if val is None:
                        return ""
                    normalized = str(val).strip()
                    # 将斜杠视为空值（常用于表示"无"或"空"）
                    if normalized == "/" or normalized == "":
                        return ""
                    return normalized

                ai_normalized = normalize_for_comparison(ai_val)
                expected_normalized = normalize_for_comparison(expected_val)
                match = ai_normalized == expected_normalized

                return {
                    "match": match,
                    "ai_value": ai_val,
                    "expected_value": expected_val,
                    "path": path
                }

        comparison = compare_values(ai_result, expected_result)

        # 计算总体准确率 - 修复：基于叶子节点计算，而不是中间节点
        def calculate_accuracy_score(comp_result):
            """递归计算准确率分数 - 统计所有叶子节点的匹配情况"""
            total_leaves = 0
            correct_leaves = 0

            def count_leaves(node):
                """递归统计叶子节点"""
                nonlocal total_leaves, correct_leaves

                if "children" in node:
                    if isinstance(node["children"], dict):
                        for child in node["children"].values():
                            count_leaves(child)
                    elif isinstance(node["children"], list):
                        for child in node["children"]:
                            count_leaves(child)
                else:
                    # 这是叶子节点
                    total_leaves += 1
                    if node.get("match", False):
                        correct_leaves += 1

            count_leaves(comp_result)
            return correct_leaves / total_leaves if total_leaves > 0 else 1.0

        accuracy_score = calculate_accuracy_score(comparison)

        field_accuracy = {
            "comparison": comparison,
            "overall_accuracy": accuracy_score,
            "calculated_at": datetime.utcnow().isoformat()
        }

        self.set_field_accuracy(field_accuracy)
        self.accuracy_score = accuracy_score

        return field_accuracy

    def get_file_info(self):
        """获取文件信息"""
        return self.file_info if self.file_info else {}

    def set_file_info(self, file_info):
        """设置文件信息"""
        self.file_info = file_info

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'filename': self.filename,
            'analysis_type': self.analysis_type,
            'ai_result': self.get_ai_result(),
            'expected_result': self.get_expected_result(),
            'system_result': self.get_system_result(),
            'comparison_result': self.get_comparison_result(),
            'field_accuracy': self.get_field_accuracy(),
            'is_first_analysis': self.is_first_analysis,
            'status': self.status,
            'file_status': self.file_status,
            'review_status': self.review_status,
            'review_priority': self.review_priority,
            'accuracy_score': float(self.accuracy_score) if self.accuracy_score else None,
            'audit_status': self.audit_status,
            'audit_comment': self.audit_comment,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class PromptConfig(db.Model):
    """提示词配置表"""
    __tablename__ = 'prompt_config'
    
    id = db.Column(db.Integer, primary_key=True, comment='主键')
    analysis_type = db.Column(db.String(50), nullable=False, comment='分析类型')
    prompt_key = db.Column(db.String(100), nullable=False, comment='提示词键名')
    prompt_content = db.Column(db.Text, nullable=False, comment='提示词内容')
    description = db.Column(db.Text, comment='描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    is_default = db.Column(db.Boolean, default=False, comment='是否为系统默认提示词')
    version = db.Column(db.String(20), default='v1.0', comment='版本号')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    __table_args__ = (
        db.UniqueConstraint('analysis_type', 'prompt_key', 'version', name='unique_type_key_version'),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'analysis_type': self.analysis_type,
            'prompt_key': self.prompt_key,
            'prompt_content': self.prompt_content,
            'description': self.description,
            'name': self.description,  # 保持兼容，使用description作为name
            'is_active': self.is_active,
            'is_default': self.is_default,
            'version': self.version,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class PromptVersion(db.Model):
    """提示词版本表"""
    __tablename__ = 'prompt_versions'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    analysis_type = db.Column(db.String(50), nullable=False, comment='分析类型')
    version = db.Column(db.String(20), nullable=False, comment='版本号')
    prompt_content = db.Column(db.Text, nullable=False, comment='提示词内容')
    description = db.Column(db.Text, comment='版本描述')
    is_active = db.Column(db.Boolean, default=False, comment='是否激活')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    usage_count = db.Column(db.Integer, default=0, comment='使用次数')
    performance_score = db.Column(db.Numeric(5, 4), comment='性能评分')

    __table_args__ = (
        db.UniqueConstraint('analysis_type', 'version', name='unique_type_version'),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'analysis_type': self.analysis_type,
            'version': self.version,
            'prompt_content': self.prompt_content,
            'description': self.description,
            'is_active': self.is_active,
            'usage_count': self.usage_count,
            'performance_score': float(self.performance_score) if self.performance_score else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class MockData(db.Model):
    """挡板数据表"""
    __tablename__ = 'mock_data'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    query_key = db.Column(db.String(255), nullable=False, comment='查询关键字')
    query_type = db.Column(db.String(50), nullable=False, comment='查询类型')
    mock_result = db.Column(db.Text, comment='挡板返回结果(JSON)')
    description = db.Column(db.Text, comment='描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def get_mock_result(self):
        """获取挡板结果"""
        if self.mock_result:
            try:
                return json.loads(self.mock_result)
            except:
                return {}
        return {}

    def set_mock_result(self, result):
        """设置挡板结果"""
        self.mock_result = json.dumps(result, ensure_ascii=False)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'query_key': self.query_key,
            'query_type': self.query_type,
            'mock_result': self.get_mock_result(),
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class CustomerSystemData(db.Model):
    """客户系统数据表"""
    __tablename__ = 'customer_system_data'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    query_key = db.Column(db.String(255), nullable=False, comment='查询关键字')
    query_type = db.Column(db.String(50), nullable=False, comment='查询类型')
    data_json = db.Column(db.Text, comment='客户系统返回内容(JSON)')
    description = db.Column(db.Text, comment='描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def get_data_json(self):
        """获取数据"""
        if self.data_json:
            try:
                return json.loads(self.data_json)
            except:
                return {}
        return {}

    def set_data_json(self, data):
        """设置数据"""
        self.data_json = json.dumps(data, ensure_ascii=False)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'query_key': self.query_key,
            'query_type': self.query_type,
            'data_json': self.get_data_json(),
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class FileTag(db.Model):
    """文件标签表"""
    __tablename__ = 'file_tags'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    file_id = db.Column(db.Integer, db.ForeignKey('analysis_records.id'), nullable=False, comment='文件ID')
    tag_name = db.Column(db.String(50), nullable=False, comment='标签名称')
    tag_color = db.Column(db.String(7), default='#2563eb', comment='标签颜色')
    tag_description = db.Column(db.Text, comment='标签描述')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    file_record = db.relationship('AnalysisRecord', backref='tags')
    creator = db.relationship('User', backref='created_tags')

    __table_args__ = (
        db.UniqueConstraint('file_id', 'tag_name', name='unique_file_tag'),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'tag_name': self.tag_name,
            'tag_color': self.tag_color,
            'tag_description': self.tag_description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ReviewRecord(db.Model):
    """复核记录表"""
    __tablename__ = 'review_records'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    record_id = db.Column(db.Integer, db.ForeignKey('analysis_records.id'), nullable=False, comment='分析记录ID')
    reviewer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='复核人ID')
    review_status = db.Column(db.Enum('approved', 'rejected', 'needs_revision'), nullable=False, comment='复核状态')
    review_comment = db.Column(db.Text, comment='复核意见')
    corrections = db.Column(db.JSON, comment='修正内容')
    review_time = db.Column(db.Numeric(8, 2), comment='复核耗时(秒)')
    auto_reviewed = db.Column(db.Boolean, default=False, comment='是否自动复核')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    analysis_record = db.relationship('AnalysisRecord', backref='review_records')
    reviewer = db.relationship('User', backref='review_records')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'record_id': self.record_id,
            'reviewer_id': self.reviewer_id,
            'review_status': self.review_status,
            'review_comment': self.review_comment,
            'corrections': self.corrections,
            'review_time': float(self.review_time) if self.review_time else None,
            'auto_reviewed': self.auto_reviewed,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class StandardAnswer(db.Model):
    """标准答案表"""
    __tablename__ = 'standard_answers'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    analysis_type = db.Column(db.String(50), nullable=False, comment='分析类型')
    file_pattern = db.Column(db.String(255), comment='文件名模式')
    standard_result = db.Column(db.JSON, nullable=False, comment='标准答案结果')
    confidence_score = db.Column(db.Numeric(5, 4), default=1.0000, comment='置信度评分')
    source_record_id = db.Column(db.Integer, db.ForeignKey('analysis_records.id'), comment='来源记录ID')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    usage_count = db.Column(db.Integer, default=0, comment='使用次数')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')

    # 关系
    source_record = db.relationship('AnalysisRecord', backref='standard_answers')
    creator = db.relationship('User', backref='created_standards')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'analysis_type': self.analysis_type,
            'file_pattern': self.file_pattern,
            'standard_result': self.standard_result,
            'confidence_score': float(self.confidence_score) if self.confidence_score else None,
            'source_record_id': self.source_record_id,
            'usage_count': self.usage_count,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ModelConfig(db.Model):
    """模型配置表"""
    __tablename__ = 'model_configs'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    model_id = db.Column(db.String(50), unique=True, nullable=False, comment='模型标识')
    model_name = db.Column(db.String(100), nullable=False, comment='模型名称')
    api_url = db.Column(db.String(255), nullable=False, comment='API地址')
    api_key = db.Column(db.String(255), nullable=False, comment='API密钥')
    vision_model = db.Column(db.String(100), comment='视觉模型名称')
    timeout = db.Column(db.Integer, default=30, comment='超时时间(秒)')
    max_tokens = db.Column(db.Integer, default=4096, comment='最大令牌数')
    temperature = db.Column(db.Numeric(3, 2), default=0.70, comment='温度参数')
    is_active = db.Column(db.Boolean, default=False, comment='是否激活')
    last_test_at = db.Column(db.DateTime, comment='最后测试时间')
    test_status = db.Column(db.Enum('success', 'failed', 'pending'), default='pending', comment='测试状态')
    response_time = db.Column(db.Numeric(8, 2), default=0.00, comment='响应时间(秒)')
    error_message = db.Column(db.Text, comment='错误信息')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'model_id': self.model_id,
            'model_name': self.model_name,
            'api_url': self.api_url,
            'api_key': self.api_key[:10] + '...' if self.api_key else None,  # 隐藏敏感信息
            'vision_model': self.vision_model,
            'timeout': self.timeout,
            'max_tokens': self.max_tokens,
            'temperature': float(self.temperature) if self.temperature else None,
            'is_active': self.is_active,
            'last_test_at': self.last_test_at.isoformat() if self.last_test_at else None,
            'test_status': self.test_status,
            'response_time': float(self.response_time) if self.response_time else None,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class DashboardStats(db.Model):
    """仪表盘统计表"""
    __tablename__ = 'dashboard_stats'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    stat_date = db.Column(db.Date, nullable=False, comment='统计日期')
    analysis_type = db.Column(db.String(50), comment='分析类型')
    total_files = db.Column(db.Integer, default=0, comment='总文件数')
    processed_files = db.Column(db.Integer, default=0, comment='已处理文件数')
    accuracy_rate = db.Column(db.Numeric(5, 4), default=0.0000, comment='识别准确率')
    avg_processing_time = db.Column(db.Numeric(8, 2), default=0.00, comment='平均处理时间(秒)')
    success_count = db.Column(db.Integer, default=0, comment='成功处理数')
    failed_count = db.Column(db.Integer, default=0, comment='失败处理数')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'stat_date': self.stat_date.isoformat() if self.stat_date else None,
            'analysis_type': self.analysis_type,
            'total_files': self.total_files,
            'processed_files': self.processed_files,
            'accuracy_rate': float(self.accuracy_rate) if self.accuracy_rate else None,
            'avg_processing_time': float(self.avg_processing_time) if self.avg_processing_time else None,
            'success_count': self.success_count,
            'failed_count': self.failed_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class UserActivity(db.Model):
    """用户活动日志表"""
    __tablename__ = 'user_activities'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    session_id = db.Column(db.String(64), comment='会话ID')
    request_id = db.Column(db.String(64), comment='请求ID')
    action = db.Column(db.String(100), nullable=False, comment='操作类型')
    resource = db.Column(db.String(255), comment='操作资源')
    details = db.Column(db.JSON, comment='操作详情')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    user = db.relationship('User', backref='activities')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'request_id': self.request_id,
            'action': self.action,
            'resource': self.resource,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class GlobalSetting(db.Model):
    """全局设置表"""
    __tablename__ = 'global_settings'

    id = db.Column(db.Integer, primary_key=True, comment='主键')
    key = db.Column(db.String(100), unique=True, nullable=False, comment='配置键')
    value = db.Column(db.Text, comment='配置值')
    description = db.Column(db.Text, comment='配置描述')
    data_type = db.Column(db.String(20), default='string', comment='数据类型')
    is_public = db.Column(db.Boolean, default=False, comment='是否公开')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def get_value(self):
        """获取配置值"""
        if self.data_type == 'json':
            try:
                return json.loads(self.value)
            except:
                return {}
        elif self.data_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes')
        elif self.data_type == 'integer':
            try:
                return int(self.value)
            except:
                return 0
        elif self.data_type == 'float':
            try:
                return float(self.value)
            except:
                return 0.0
        return self.value

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.get_value(),
            'description': self.description,
            'data_type': self.data_type,
            'is_public': self.is_public,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
