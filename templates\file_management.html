{% extends "base.html" %}

{% block title %}文件列表管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    /* 现代化文件管理样式 - 与系统风格保持一致 */
    .file-management-container {
        background: var(--gray-50);
        min-height: 100vh;
        padding: 0;
    }

    .management-header {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }

    .management-header h2 {
        color: var(--gray-900);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .management-header p {
        color: var(--gray-600);
        margin-bottom: 0;
    }

    .control-panel {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
    }

    .file-table {
        background: white;
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
    }

    .file-table table {
        margin-bottom: 0;
        color: var(--gray-900);
    }

    .file-table th {
        background: var(--gray-50);
        border-color: var(--gray-200);
        color: var(--gray-700);
        font-weight: 600;
        padding: 1rem 0.75rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .file-table td {
        border-color: var(--gray-100);
        padding: 0.75rem;
        vertical-align: middle;
    }

    .file-table tbody tr:hover {
        background: var(--gray-50);
    }

    .file-table tbody tr.file-deprecated {
        opacity: 0.6;
        background: rgba(239, 68, 68, 0.05);
    }

    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .status-pending {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .status-completed {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .status-processing {
        background: rgba(6, 182, 212, 0.1);
        color: var(--info-color);
        border: 1px solid rgba(6, 182, 212, 0.2);
    }

    .status-failed {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .status-pending-audit {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .status-pending-review {
        background: rgba(139, 92, 246, 0.1);
        color: #8b5cf6;
        border: 1px solid rgba(139, 92, 246, 0.2);
    }

    /* 分析类型颜色样式 - 商务风格 */
    .analysis-type-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
        text-align: center;
        min-width: 80px;
        white-space: nowrap;
    }

    /* 期货账户/开户文件解析 - 深蓝色 */
    .analysis-type-futures_account {
        background: rgba(30, 64, 175, 0.1);
        color: #1e40af;
        border-color: rgba(30, 64, 175, 0.2);
    }

    /* 理财产品说明书 - 青色 */
    .analysis-type-wealth_management {
        background: rgba(6, 182, 212, 0.1);
        color: #0891b2;
        border-color: rgba(6, 182, 212, 0.2);
    }

    /* 券商账户计息变更 - 紫色 */
    .analysis-type-broker_interest {
        background: rgba(124, 58, 237, 0.1);
        color: #7c3aed;
        border-color: rgba(124, 58, 237, 0.2);
    }

    /* 账户开户场景 - 绿色 */
    .analysis-type-account_opening {
        background: rgba(5, 150, 105, 0.1);
        color: #059669;
        border-color: rgba(5, 150, 105, 0.2);
    }

    /* 宁银理财费用变更 - 橙色 */
    .analysis-type-ningyin_fee {
        background: rgba(234, 88, 12, 0.1);
        color: #ea580c;
        border-color: rgba(234, 88, 12, 0.2);
    }

    /* 非标交易确认单解析 - 灰色 */
    .analysis-type-non_standard_trade {
        background: rgba(75, 85, 99, 0.1);
        color: #4b5563;
        border-color: rgba(75, 85, 99, 0.2);
    }

    .btn-tech {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border: none;
        color: white;
        border-radius: var(--border-radius);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .btn-tech:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        color: white;
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    }

    /* 新弹窗样式 */
    .stats-section .stat-item {
        padding: 0.5rem;
        border-radius: var(--border-radius);
        background: white;
        border: 1px solid var(--gray-200);
    }

    .stats-section .stat-label {
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .stats-section .stat-value {
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    }

    /* 五列字段渲染布局样式 */
    .result-columns {
        /* 移除固定最小高度，让内容适应容器 */
        height: 100%;
    }

    .result-column {
        min-width: 0; /* 防止内容溢出 */
    }

    .result-column h6 {
        font-size: 0.875rem;
        font-weight: 600;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .result-content {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    /* 字段渲染样式 */
    .field-row {
        display: flex;
        align-items: flex-start;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--gray-100);
        min-height: 2.5rem;
    }

    .field-row:last-child {
        border-bottom: none;
    }

    .field-name {
        font-weight: 600;
        color: var(--gray-700);
        font-size: 0.8rem;
        line-height: 1.4;
        word-break: break-word;
        padding: 0.25rem 0;
    }

    .field-value {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 0.8rem;
        line-height: 1.4;
        color: var(--gray-800);
        word-break: break-word;
        padding: 0.25rem 0;
        background: var(--gray-50);
        border-radius: 4px;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem 0;
    }

    .field-value.ai-value {
        background: #e3f2fd;
        border-left: 3px solid #2196f3;
    }

    .field-value.expected-value {
        background: #e8f5e8;
        border-left: 3px solid #4caf50;
    }

    .comparison-status {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        margin: 0.125rem 0;
    }

    .comparison-status.match {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .comparison-status.mismatch {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .comparison-status i {
        margin-right: 0.25rem;
    }

    /* 嵌套字段样式 */
    .field-name.nested-field {
        padding-left: 1rem;
        color: var(--gray-600);
        font-size: 0.75rem;
    }

    .field-name.nested-field::before {
        content: "└─ ";
        color: var(--gray-400);
    }

    /* 字段分组样式 */
    .field-group-header {
        background: linear-gradient(135deg, var(--primary-color), #4f46e5);
        color: white;
        padding: 0.5rem 0.75rem;
        margin: 0.5rem 0 0.25rem 0;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .field-group-header:first-child {
        margin-top: 0;
    }

    .field-group-content {
        margin-bottom: 0.5rem;
    }

    /* 数组项标题样式 */
    .array-item-header {
        background: linear-gradient(135deg, #00b894, #00cec9);
        color: white;
        padding: 10px 15px;
        font-weight: 700;
        font-size: 15px;
        border-radius: 8px;
        margin: 15px 0 8px 0;
        box-shadow: 0 3px 6px rgba(0, 184, 148, 0.3);
        border-left: 4px solid #00a085;
        position: relative;
    }

    .array-item-header:first-child {
        margin-top: 0;
    }

    .array-item-header::before {
        content: "📋";
        margin-right: 8px;
        font-size: 14px;
    }

    .field-group-content .field-row {
        border-left: 3px solid var(--primary-color);
        padding-left: 0.5rem;
        margin-left: 0.25rem;
    }

    .field-group-content .field-row:hover {
        background: var(--gray-50);
        border-radius: 4px;
    }

    /* 底部栏样式 - 确保不使用sticky定位 */
    .modal-footer-custom {
        position: static !important;
        flex-shrink: 0 !important;
        z-index: 10;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        display: flex !important;
        align-items: center !important;
    }

    /* 确保弹窗固定比例布局正确 */
    #resultModal .modal-dialog {
        height: 95vh !important;
        margin: 2.5vh auto !important;
    }

    #resultModal .modal-content {
        height: 100% !important;
        overflow: hidden !important;
        display: flex !important;
        flex-direction: column !important;
    }

    #resultModal .modal-header {
        height: 8% !important;
        min-height: 60px !important;
        max-height: 80px !important;
        flex-shrink: 0 !important;
    }

    #resultModal .stats-section {
        height: 12% !important;
        min-height: 80px !important;
        max-height: 120px !important;
        flex-shrink: 0 !important;
    }

    #resultModal .modal-body {
        height: 70% !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    #resultModal .modal-footer-custom {
        height: 10% !important;
        min-height: 70px !important;
        max-height: 100px !important;
        flex-shrink: 0 !important;
    }

    #resultModal .result-columns {
        overflow: hidden !important;
    }

    /* 确保四列内容区域滚动正常 */
    .four-columns-content {
        scrollbar-width: thin;
        scrollbar-color: var(--gray-400) var(--gray-100);
    }

    .four-columns-content::-webkit-scrollbar {
        width: 8px;
    }

    .four-columns-content::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 4px;
    }

    .four-columns-content::-webkit-scrollbar-thumb {
        background: var(--gray-400);
        border-radius: 4px;
    }

    .four-columns-content::-webkit-scrollbar-thumb:hover {
        background: var(--gray-500);
    }

    /* 确保固定比例布局的稳定性 */
    #resultModal * {
        box-sizing: border-box !important;
    }

    #resultModal .modal-dialog {
        display: flex !important;
        align-items: center !important;
    }

    #resultModal .modal-content {
        margin: 0 !important;
        border-radius: 8px !important;
    }

    /* 防止内容溢出 */
    #resultModal .modal-header,
    #resultModal .stats-section,
    #resultModal .modal-footer-custom {
        overflow: hidden !important;
    }

    #resultModal .modal-body {
        min-height: 0 !important;
    }

    /* 确保主要内容底部有足够间隙 */
    #resultModal .modal-body {
        height: 68% !important;
    }

    #resultModal #fourColumnsContentContainer {
        padding-bottom: 1rem !important;
    }

    #resultModal #originalFileContent {
        padding-bottom: 1rem !important;
    }

    .footer-left, .footer-right {
        flex-shrink: 0;
    }

    .footer-left {
        flex: 1;
        max-width: 600px;
    }

    .modal-footer-custom .btn {
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .modal-footer-custom .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .modal-footer-custom .form-control {
        border-radius: 6px;
        border: 1px solid var(--gray-300);
        transition: border-color 0.2s ease;
    }

    .modal-footer-custom .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }

    .modal-footer-custom .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* 合并分组标题样式 */
    .merged-group-header-row {
        position: relative;
    }

    .merged-group-header {
        position: relative;
        z-index: 2;
    }

    .four-column-data-row {
        position: relative;
    }

    .four-column-data-row .field-row-aligned {
        margin: 0.25rem 0;
        border-radius: 4px;
        border-left: 3px solid var(--primary-color);
        background: var(--gray-50);
        display: flex;
        align-items: center;
        position: relative;
        transition: all 0.2s ease;
    }

    /* 四列布局中非备注字段的默认高度 - 允许JavaScript覆盖 */
    .four-column-data-row .field-row-aligned:not(.remarks-field) {
        height: 38px;
        min-height: 38px;
        max-height: 38px;
    }

    /* 四列容器中的备注字段特殊样式 - 允许JavaScript覆盖 */
    .four-column-data-row .field-row-aligned.remarks-field {
        height: 38px;
        min-height: 38px;
        max-height: 38px;
        align-items: center !important;
    }

    /* 当JavaScript设置了同步高度时，使用同步高度 - 提高优先级 */
    .four-column-data-row .field-row-aligned.height-synced:not(.remarks-field) {
        height: var(--synced-height) !important;
        min-height: var(--synced-height) !important;
        max-height: var(--synced-height) !important;
        align-items: center !important;
    }

    .four-column-data-row .field-row-aligned.height-synced.remarks-field {
        height: var(--synced-height) !important;
        min-height: var(--synced-height) !important;
        max-height: var(--synced-height) !important;
        align-items: center !important;
    }

    .four-column-data-row .field-row-aligned.remarks-field > div,
    .four-column-data-row .field-row-aligned.remarks-field > textarea,
    .four-column-data-row .field-row-aligned.remarks-field > input {
        height: auto !important;
        min-height: 32px !important;
        max-height: none !important;
        white-space: pre-wrap !important;
        word-break: break-word !important;
        overflow: visible !important;
        text-overflow: unset !important;
    }

    /* 强制覆盖所有可能的高度限制 */
    .remarks-field,
    .remarks-field > *,
    .four-column-data-row .remarks-field,
    .four-column-data-row .remarks-field > * {
        height: auto !important;
        max-height: none !important;
    }

    .four-column-data-row .field-row-aligned:hover {
        background: rgba(var(--primary-color-rgb), 0.05) !important;
        transform: translateX(2px);
    }

    .four-column-data-row .field-row-aligned.field-name {
        border-left-color: #6366f1;
    }

    .four-column-data-row .field-row-aligned.ai-value {
        border-left-color: #3b82f6;
    }

    .four-column-data-row .field-row-aligned.expected-value {
        border-left-color: #10b981;
    }

    .four-column-data-row .field-row-aligned.comparison-result {
        border-left-color: #f59e0b;
    }

    /* 自动调整高度的 textarea 样式 */
    .auto-resize-textarea {
        resize: vertical;
        overflow-y: auto;
        min-height: 32px;
        height: auto;
        font-family: 'Consolas', 'Monaco', monospace;
        line-height: 1.4;
    }

    /* 五列对齐样式（保留备用） */
    .five-columns-container {
        position: relative;
    }

    .five-columns-header {
        background: var(--gray-50);
        position: sticky;
        top: 0;
        z-index: 5;
    }

    .column-header {
        background: var(--gray-50);
        font-weight: 600;
    }

    .five-columns-content {
        background: white;
    }

    .column-content {
        background: white;
        overflow-wrap: break-word;
    }

    /* 分组单元格样式 */
    .group-cell-merged {
        position: relative;
    }

    .empty-cell {
        visibility: hidden;
    }

    /* 四列对齐样式（保留备用） */
    .four-columns-container {
        position: relative;
    }

    .four-columns-header {
        background: var(--gray-50);
        position: sticky;
        top: 0;
        z-index: 5;
    }

    .four-columns-content {
        background: white;
    }

    /* 展开/收起按钮样式 */
    .toggle-original-btn,
    .expand-original-btn {
        transition: all 0.3s ease !important;
    }

    .toggle-original-btn:hover,
    .expand-original-btn:hover {
        background: #4f46e5 !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    }

    .toggle-original-btn:active,
    .expand-original-btn:active {
        transform: scale(0.95) !important;
    }

    /* 原件区域过渡动画 */
    .result-columns {
        transition: grid-template-columns 0.3s ease !important;
    }

    #originalFileArea {
        transition: all 0.3s ease !important;
    }

    .field-row-aligned {
        transition: background-color 0.2s ease;
    }

    /* 非备注字段的默认高度 - 允许JavaScript覆盖 */
    .field-row-aligned:not(.remarks-field) {
        height: 38px;
        min-height: 38px;
        max-height: 38px;
    }

    /* 备注字段特殊样式 - 允许自适应高度 */
    .field-row-aligned.remarks-field {
        height: 38px;
        min-height: 38px;
        max-height: 38px;
        align-items: center !important;
    }

    /* 当JavaScript设置了同步高度时，优先使用同步高度 - 提高优先级 */
    .field-row-aligned.height-synced:not(.remarks-field) {
        height: var(--synced-height) !important;
        min-height: var(--synced-height) !important;
        max-height: var(--synced-height) !important;
        align-items: center !important;
    }

    .field-row-aligned.height-synced.remarks-field {
        height: var(--synced-height) !important;
        min-height: var(--synced-height) !important;
        max-height: var(--synced-height) !important;
        align-items: center !important;
    }

    .field-row-aligned.remarks-field > div,
    .field-row-aligned.remarks-field > textarea {
        height: auto !important;
        min-height: 32px !important;
        white-space: pre-wrap !important;
        word-break: break-word !important;
        overflow: visible !important;
        text-overflow: unset !important;
    }

    .field-row-aligned:hover {
        background: rgba(var(--primary-color-rgb), 0.05) !important;
    }

    .field-row-aligned.field-name {
        border-left-color: #6366f1;
    }

    .field-row-aligned.field-name > div {
        font-weight: 500;
    }

    .field-row-aligned.ai-value {
        border-left-color: #3b82f6;
    }

    .field-row-aligned.expected-value {
        border-left-color: #10b981;
    }

    .field-row-aligned.comparison-result {
        border-left-color: #f59e0b;
    }

    .field-row-aligned.comparison-result > div {
        text-align: center;
        font-weight: 500;
        width: 100%;
    }

    /* 确保所有字段行内的元素都有统一高度 - 排除备注字段 */
    .field-row-aligned:not(.remarks-field) input,
    .field-row-aligned:not(.remarks-field) > div {
        height: 32px;
        box-sizing: border-box;
    }

    /* 当行高度同步时，内部元素也要适应 - 提高优先级 */
    .field-row-aligned.height-synced input,
    .field-row-aligned.height-synced > div,
    .four-column-data-row .field-row-aligned.height-synced input,
    .four-column-data-row .field-row-aligned.height-synced > div {
        height: calc(var(--synced-height) - 6px) !important;
        box-sizing: border-box;
    }

    /* 备注字段内的元素允许自适应高度 */
    .field-row-aligned.remarks-field input,
    .field-row-aligned.remarks-field > div,
    .field-row-aligned.remarks-field textarea {
        height: auto !important;
        min-height: 32px !important;
        max-height: none !important;
    }

    /* 可编辑字段特殊样式 */
    .field-row-aligned .editable-field {
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .field-row-aligned .editable-field:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
        outline: none;
    }

    .field-group-header-aligned:first-child {
        margin-top: 0;
    }

    /* 滚动条样式 */
    .five-columns-content::-webkit-scrollbar,
    .four-columns-content::-webkit-scrollbar {
        width: 8px;
    }

    .five-columns-content::-webkit-scrollbar-track,
    .four-columns-content::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 4px;
    }

    .five-columns-content::-webkit-scrollbar-thumb,
    .four-columns-content::-webkit-scrollbar-thumb {
        background: var(--gray-400);
        border-radius: 4px;
    }

    .five-columns-content::-webkit-scrollbar-thumb:hover,
    .four-columns-content::-webkit-scrollbar-thumb:hover {
        background: var(--gray-500);
    }

    /* 响应式布局 - 保持固定比例 */
    @media (max-width: 768px) {
        #resultModal .modal-dialog {
            height: 90vh !important;
            margin: 5vh auto !important;
        }

        #resultModal .modal-header {
            height: 10% !important;
            min-height: 50px !important;
            max-height: 70px !important;
            padding: 0.75rem 1rem !important;
        }

        #resultModal .stats-section {
            height: 15% !important;
            min-height: 70px !important;
            max-height: 100px !important;
            padding: 0.75rem 1rem !important;
        }

        #resultModal .modal-body {
            height: 63% !important;
        }

        #resultModal .modal-footer-custom {
            height: 10% !important;
            min-height: 60px !important;
            max-height: 80px !important;
            padding: 0.75rem 1rem !important;
        }

        .modal-footer-custom .d-flex {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .footer-left {
            order: 1;
            max-width: 100%;
        }

        .footer-right {
            order: 2;
            align-self: flex-end;
        }

        .footer-left .d-flex {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .footer-left .form-control {
            width: 100% !important;
        }
    }

    @media (max-width: 576px) {
        #resultModal .modal-dialog {
            height: 85vh !important;
            margin: 7.5vh auto !important;
        }

        #resultModal .modal-header {
            height: 12% !important;
            min-height: 45px !important;
            max-height: 60px !important;
            padding: 0.5rem !important;
        }

        #resultModal .stats-section {
            height: 18% !important;
            min-height: 60px !important;
            max-height: 90px !important;
            padding: 0.5rem !important;
        }

        #resultModal .modal-body {
            height: 58% !important;
        }

        #resultModal .modal-footer-custom {
            height: 10% !important;
            min-height: 50px !important;
            max-height: 70px !important;
            padding: 0.5rem !important;
        }

        .footer-right .d-flex {
            flex-direction: column;
            gap: 0.25rem !important;
        }

        .footer-right .btn {
            width: 100%;
            padding: 0.25rem 0.5rem !important;
            font-size: 0.8rem !important;
        }

        .footer-right {
            align-self: stretch;
        }

        .modal-title {
            font-size: 1rem !important;
        }

        .stat-value {
            font-size: 1.2rem !important;
        }
    }

    /* 原件展示特殊样式 */
    #originalFileContent {
        background: var(--gray-50);
        border-radius: var(--border-radius);
    }

    #originalFileContent img {
        border-radius: var(--border-radius);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .audit-controls {
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    }

    .audit-controls .btn {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
    }

    .audit-controls .btn-success:hover {
        background-color: #16a34a;
        border-color: #16a34a;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
    }

    .audit-controls .btn-danger:hover {
        background-color: #dc2626;
        border-color: #dc2626;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
        .result-columns {
            grid-template-columns: 1fr 350px !important;
        }
    }

    @media (max-width: 768px) {
        .stats-section .row {
            text-align: center;
        }

        .stats-section .stat-value {
            font-size: 1.25rem !important;
        }

        .result-columns {
            grid-template-columns: 1fr !important;
            grid-template-rows: 1fr auto;
            gap: 0.5rem;
            padding: 0.5rem;
        }

        .result-column {
            padding: 0.75rem !important;
        }

        .result-column h6 {
            font-size: 0.8rem;
            margin-bottom: 0.5rem !important;
            padding-bottom: 0.25rem !important;
        }

        .result-content {
            font-size: 0.75rem;
        }

        #originalFileArea {
            max-height: 300px;
        }
    }

    .btn-tech-outline {
        background: white;
        border: 1px solid var(--gray-300);
        color: var(--gray-700);
        border-radius: var(--border-radius);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .btn-tech-outline:hover {
        background: var(--primary-bg);
        border-color: var(--primary-color);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .tech-switch {
        position: relative;
        width: 50px;
        height: 24px;
        background: var(--gray-300);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .tech-switch.active {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    }

    .tech-switch .switch-handle {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .tech-switch.active .switch-handle {
        transform: translateX(26px);
    }

    .tech-checkbox {
        appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid var(--gray-300);
        border-radius: 4px;
        background: white;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
    }

    .tech-checkbox:checked {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border-color: var(--primary-color);
    }

    .tech-checkbox:checked::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .search-box {
        background: white;
        border: 1px solid var(--gray-300);
        color: var(--gray-900);
        border-radius: var(--border-radius);
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        background: white;
        border-color: var(--primary-color);
        color: var(--gray-900);
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.1);
    }

    .search-box::placeholder {
        color: var(--gray-500);
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(4px);
    }

    .loading-spinner {
        color: var(--primary-color);
        font-size: 2rem;
    }

    /* 旋转动画样式 */
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .spinning {
        animation: spin 1s linear infinite;
    }

    /* 准确率指示器样式 */
    .accuracy-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .accuracy-bar {
        width: 60px;
        height: 6px;
        background: var(--gray-200);
        border-radius: 3px;
        overflow: hidden;
    }

    .accuracy-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .accuracy-fill.high {
        background: linear-gradient(90deg, var(--success-color), #10b981);
    }

    .accuracy-fill.medium {
        background: linear-gradient(90deg, var(--warning-color), #f59e0b);
    }

    .accuracy-fill.low {
        background: linear-gradient(90deg, var(--danger-color), #ef4444);
    }

    /* 文件操作按钮组 */
    .file-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .file-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
        min-width: 70px;
        text-align: center;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
    }

    /* 置灰状态样式 - 简化版本，只改变底色 */
    .file-deprecated {
        background-color: #f8f9fa !important; /* 浅灰色背景 */
    }

    .file-deprecated:hover {
        background-color: #e9ecef !important; /* 悬停时稍深一点的灰色 */
    }

    /* 废弃文件的按钮禁用样式 */
    .file-deprecated .btn:not(.btn-view-result):not(.btn-restore) {
        opacity: 0.5;
        pointer-events: none;
        cursor: not-allowed;
        background: #9ca3af !important;
        border-color: #9ca3af !important;
    }

    /* 废弃文件的复选框禁用样式 */
    .file-deprecated .tech-checkbox {
        opacity: 0.6;
        pointer-events: none;
        cursor: not-allowed;
    }

    /* 按钮颜色样式 - 商务风格 */

    /* 分析按钮 - 绿色 */
    .btn-analyze {
        background: #059669;
        border: 1px solid #059669;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-analyze:hover {
        background: #047857;
        border-color: #047857;
        color: white;
    }

    /* 重分析按钮 - 深蓝色 */
    .btn-reanalyze {
        background: #1e40af;
        border: 1px solid #1e40af;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-reanalyze:hover {
        background: #1d4ed8;
        border-color: #1d4ed8;
        color: white;
    }

    /* 查看结果按钮 - 青色 */
    .btn-view-result {
        background: #0891b2;
        border: 1px solid #0891b2;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-view-result:hover {
        background: #0e7490;
        border-color: #0e7490;
        color: white;
    }

    /* 审核按钮 - 紫色 */
    .btn-audit {
        background: #7c3aed;
        border: 1px solid #7c3aed;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-audit:hover {
        background: #6d28d9;
        border-color: #6d28d9;
        color: white;
    }

    /* 废弃按钮 - 橙红色 */
    .btn-deprecate {
        background: #dc2626;
        border: 1px solid #dc2626;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-deprecate:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        color: white;
    }

    /* 恢复按钮 - 天蓝色 */
    .btn-restore {
        background: #0ea5e9;
        border: 1px solid #0ea5e9;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-restore:hover {
        background: #0284c7;
        border-color: #0284c7;
        color: white;
    }

    /* 禁用状态的按钮 */
    .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
        pointer-events: none;
    }

    /* 禁用状态的查看结果按钮 - 保持原色但变浅 */
    .btn-view-result:disabled {
        background: #0891b2;
        border-color: #0891b2;
        color: white;
        opacity: 0.4;
    }

    /* 控制面板按钮样式 */
    .btn-refresh {
        background: linear-gradient(135deg, #64748b 0%, #475569 100%);
        border: 1px solid #64748b;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-refresh:hover {
        background: linear-gradient(135deg, #475569 0%, #334155 100%);
        border-color: #475569;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(100, 116, 139, 0.2);
    }

    .btn-upload {
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        border: 1px solid #7c3aed;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-upload:hover {
        background: linear-gradient(135deg, #6d28d9 0%, #5b21b6 100%);
        border-color: #6d28d9;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(124, 58, 237, 0.2);
    }

    .btn-search {
        background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
        border: 1px solid #1e40af;
        color: white;
        transition: all 0.2s ease;
    }

    .btn-search:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
        border-color: #1d4ed8;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(30, 64, 175, 0.2);
    }

    /* 现代化卡片样式 */
    .modern-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
    }

    .modern-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    /* 表单控件现代化样式 */
    .form-control, .form-select {
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        padding: 0.5rem 0.75rem;
        transition: all 0.3s ease;
        background: white;
        color: var(--gray-900);
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.1);
        outline: none;
    }

    /* 输入组样式 */
    .input-group .btn {
        border-left: none;
    }

    .input-group .form-control:focus + .btn {
        border-color: var(--primary-color);
    }

    /* 分页信息样式 */
    #paginationInfo {
        color: var(--gray-600);
        font-size: 0.875rem;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .management-header {
            padding: 1.5rem;
        }

        .control-panel {
            padding: 1.5rem;
        }

        .file-actions {
            flex-direction: column;
            gap: 0.25rem;
        }

        .file-actions .btn {
            width: 100%;
            font-size: 0.75rem;
        }
    }



    /* Office文件预览样式 */
    .office-preview-container {
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        max-height: 600px;
        overflow-y: auto;
    }

    .office-preview-container::-webkit-scrollbar {
        width: 8px;
    }

    .office-preview-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .office-preview-container::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .office-preview-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }



    /* 按钮激活状态 */
    .btn-magnifier-active {
        background-color: var(--primary-color) !important;
        color: white !important;
        border-color: var(--primary-color) !important;
    }

    /* 全屏模态框样式 */
    .modal-fullscreen .modal-body {
        padding: 0;
    }

    #fullscreenContent iframe {
        width: 100%;
        height: 100%;
        border: none;
    }

    #fullscreenContent img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    /* 数组项样式 */
    .array-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        padding: 2px 0;
    }

    .array-item:last-child {
        margin-bottom: 0;
    }

    .array-item-text {
        font-size: 0.9em;
        line-height: 1.3;
        word-break: break-word;
    }

    .array-item .badge {
        font-size: 0.7em;
        min-width: 20px;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="file-management-container">
    <div class="container-fluid">

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <div class="input-group">
                        <input type="text" class="form-control search-box" id="fileSearchInput"
                               placeholder="搜索文件名、分析类型...">
                        <button class="btn btn-search" onclick="searchFiles()">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select search-box" id="analysisTypeFilter" onchange="applyFilters()">
                        <option value="">全部分析类型</option>
                        <option value="futures_account">期货账户/开户文件解析</option>
                        <option value="wealth_management">理财产品说明书</option>
                        <option value="broker_interest">券商账户计息变更</option>
                        <option value="account_opening">账户开户场景</option>
                        <option value="ningyin_fee">宁银理财费用变更</option>
                        <option value="non_standard_trade">非标交易确认单解析</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select search-box" id="statusFilter" onchange="applyFilters()">
                        <option value="">全部状态</option>
                        <option value="pending">待分析</option>
                        <option value="uploaded">已上传</option>
                        <option value="processing">处理中</option>
                        <option value="analyzing">分析中</option>
                        <option value="completed">已完成</option>
                        <option value="analyzed">已分析</option>
                        <option value="pending_audit">待审核</option>
                        <option value="pending_review">待复核</option>
                        <option value="audit_rejected">审核不通过</option>
                        <option value="review_rejected">复核不通过</option>
                        <option value="failed">失败</option>
                        <option value="deprecated">已废弃</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select search-box" id="fileStatusFilter" onchange="applyFilters()">
                        <option value="all" selected>全部文件</option>
                        <option value="active">正常文件</option>
                        <option value="deprecated">废弃文件</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2 justify-content-end">
                        <button class="btn btn-refresh" onclick="refreshFileList()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新
                        </button>
                        <button class="btn btn-warning" onclick="autoAuditFiles()" title="自动审核100%识别率的待审核文件">
                            <i class="bi bi-magic me-2"></i>一键自动审核
                        </button>
                        <button class="btn btn-upload" onclick="goToDocumentAnalysis()">
                            <i class="bi bi-plus-circle me-2"></i>上传文件
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件列表表格 -->
        <div class="file-table">
            <table class="table mb-0">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <input type="checkbox" class="tech-checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                        </th>
                        <th>文件名</th>
                        <th>分析类型</th>
                        <th>状态</th>
                        <th>准确率</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="fileTableBody">
                    <!-- 文件记录将在这里动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="d-flex justify-content-between align-items-center" id="paginationContainer">
            <div class="pagination-info-section">
                <span id="paginationInfo">第 1 页，共 1 页，总计 0 条记录</span>
                <div class="per-page-selector">
                    <span>每页显示</span>
                    <select id="perPageSelect" onchange="changePerPage()">
                        <option value="10">10条</option>
                        <option value="20" selected>20条</option>
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                    </select>
                </div>
            </div>
            <nav>
                <ul class="pagination mb-0" id="paginationList">
                    <!-- 分页按钮将在这里动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 固定比例布局的文件分析结果对比弹窗 -->
<div class="modal fade" id="resultModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-fullscreen-lg-down" style="max-width: 98%; width: 1600px; height: 95vh; margin: 2.5vh auto;">
        <div class="modal-content" style="background: white; border: 1px solid var(--gray-200); color: var(--gray-900); height: 100%; display: flex; flex-direction: column; overflow: hidden;">

            <!-- 头部栏 - 固定比例 8% -->
            <div class="modal-header" style="
                border-bottom: 1px solid var(--gray-200);
                background: var(--gray-50);
                height: 8%;
                min-height: 60px;
                max-height: 80px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                padding: 0 1.5rem;
            ">
                <h5 class="modal-title">
                    <i class="bi bi-clipboard-data me-2" style="color: var(--primary-color);"></i>
                    文件分析结果对比
                    <span class="badge bg-secondary ms-2" id="modalFileName">文件名</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>

            <!-- 统计信息区域 - 固定比例 12% -->
            <div class="stats-section" style="
                background: var(--gray-50);
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--gray-200);
                height: 12%;
                min-height: 80px;
                max-height: 120px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
            ">
                <div class="row g-3 w-100">
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">分析类型</div>
                            <div class="stat-value" id="fileAccuracy" style="font-size: 1.5rem; font-weight: 600; color: var(--primary-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">全字段正确率</div>
                            <div class="stat-value" id="fieldAccuracy" style="font-size: 1.5rem; font-weight: 600; color: var(--success-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">正确字段</div>
                            <div class="stat-value" id="correctFields" style="font-size: 1.5rem; font-weight: 600; color: var(--success-color);">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-label" style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">总字段数</div>
                            <div class="stat-value" id="totalFields" style="font-size: 1.5rem; font-weight: 600; color: var(--gray-600);">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 - 固定比例 70% (使用剩余空间) -->
            <div class="modal-body" style="
                padding: 1rem;
                height: 70%;
                overflow: hidden;
                flex-shrink: 0;
                box-sizing: border-box;
                position: relative;
            ">
                <!-- 四列字段渲染区域 -->
                <div class="result-columns" id="resultColumnsContainer" style="
                    display: grid;
                    grid-template-columns: 1fr 450px;
                    gap: 1rem;
                    height: 100%;
                    transition: grid-template-columns 0.3s ease;
                    overflow: hidden;
                ">
                    <!-- 前四列统一滚动区域 -->
                    <div class="four-columns-container" style="display: flex; flex-direction: column; background: white; border-radius: var(--border-radius); border: 1px solid var(--gray-200); box-shadow: var(--shadow-sm); overflow: hidden; position: relative; height: 100%;">
                        <!-- 收起状态下的展开按钮 - 与收起按钮同一高度 -->
                        <button class="expand-original-btn" id="expandOriginalBtn" onclick="toggleOriginalFile()" style="
                            position: absolute;
                            right: 8px;
                            top: 8px;
                            width: 32px;
                            height: 32px;
                            background: var(--primary-color);
                            border: none;
                            border-radius: 6px;
                            color: white;
                            cursor: pointer;
                            z-index: 10;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                            transition: all 0.3s ease;
                        " title="展开原件展示">
                            <i class="bi bi-chevron-right" style="font-size: 1rem;"></i>
                        </button>

                        <!-- 四列标题行 -->
                        <div class="four-columns-header" style="display: grid; grid-template-columns: 1.5fr 2fr 2fr 1fr; border-bottom: 1px solid var(--gray-200); flex-shrink: 0;">
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-list-ul me-2"></i>字段名称
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-robot me-2"></i>AI识别内容
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem; border-right: 1px solid var(--gray-200);">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-target me-2"></i>预期正确结果
                                </h6>
                            </div>
                            <div class="column-header" style="padding: 1rem;">
                                <h6 style="color: var(--primary-color); margin: 0; font-size: 0.875rem;">
                                    <i class="bi bi-graph-up me-2"></i>对比结果
                                </h6>
                            </div>
                        </div>

                        <!-- 四列内容区域（统一滚动） -->
                        <div class="four-columns-content" style="flex: 1; overflow-y: auto; overflow-x: hidden; max-height: 100%;">
                            <div id="fourColumnsContentContainer" style="padding-bottom: 1rem;">
                                <!-- 动态生成的四列内容将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <!-- 原件展示列（独立滚动，支持展开/收起） -->
                    <div class="result-column" id="originalFileArea" style="background: white; border-radius: var(--border-radius); padding: 1rem; border: 1px solid var(--gray-200); box-shadow: var(--shadow-sm); display: flex; flex-direction: column; position: relative; transition: all 0.3s ease;">
                        <!-- 展开/收起按钮 - 放在左上角 -->
                        <button class="toggle-original-btn" id="toggleOriginalBtn" onclick="toggleOriginalFile()" style="
                            position: absolute;
                            left: 8px;
                            top: 8px;
                            width: 32px;
                            height: 32px;
                            background: var(--primary-color);
                            border: none;
                            border-radius: 6px;
                            color: white;
                            cursor: pointer;
                            z-index: 10;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                            transition: all 0.3s ease;
                        " title="收起原件展示">
                            <i class="bi bi-chevron-left" id="toggleIcon" style="font-size: 1rem;"></i>
                        </button>

                        <h6 style="color: var(--primary-color); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 1px solid var(--gray-200); flex-shrink: 0; display: flex; align-items: center; justify-content: space-between; padding-left: 3rem;">
                            <span>
                                <i class="bi bi-file-earmark-image me-2"></i>原件展示
                                <!-- 多文件指示器 -->
                                <span id="multiFileIndicator" class="badge bg-info text-white ms-2" style="font-size: 0.75rem; display: none;">
                                    <i class="bi bi-files me-1"></i>
                                    <span id="currentFileIndex">1</span>/<span id="totalFileCount">1</span>
                                </span>
                            </span>
                            <div class="d-flex gap-2">
                                <!-- 文件切换按钮组 -->
                                <div id="fileNavigationControls" class="btn-group" style="display: none;">
                                    <button class="btn btn-sm btn-outline-secondary" id="prevFileBtn" onclick="switchToPreviousFile()" title="上一个文件">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" id="nextFileBtn" onclick="switchToNextFile()" title="下一个文件">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </div>
                                <!-- 文件选择按钮 -->
                                <div id="fileSelectDropdown" style="display: none;">
                                    <button class="btn btn-sm btn-outline-primary" type="button" onclick="toggleFileSelector()" id="fileSelectorBtn">
                                        <i class="bi bi-list me-1"></i>选择文件
                                    </button>
                                </div>

                                <button type="button" class="btn btn-sm btn-outline-secondary" id="fullscreenBtn" onclick="toggleFullscreen()" title="全屏查看">
                                    <i class="bi bi-arrows-fullscreen" id="fullscreenIcon"></i>
                                </button>
                            </div>
                        </h6>
                        <div class="result-content" id="originalFileContent" style="flex: 1; overflow: auto; background: var(--gray-50); position: relative; padding: 0.5rem;">
                            <!-- 原件将在这里显示 -->
                            <div class="text-center text-muted" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <i class="bi bi-file-earmark-image" style="font-size: 3rem; opacity: 0.3;"></i>
                                <p class="mt-2">原件加载中...</p>
                                <p class="small text-primary mt-2">
                                    <i class="bi bi-info-circle me-1"></i>
                                    点击左上角按钮可收起此区域
                                </p>
                            </div>


                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部栏 - 固定比例 10% -->
            <div class="modal-footer-custom" style="
                background: var(--gray-50);
                border-top: 1px solid var(--gray-200);
                padding: 1rem 1.5rem;
                height: 10%;
                min-height: 70px;
                max-height: 100px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                position: relative;
                z-index: 1000;
            ">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <!-- 左侧：审核说明和自动切换 -->
                    <div class="footer-left d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center gap-2">
                            <label class="form-label mb-0" style="font-size: 0.875rem; color: var(--gray-600); white-space: nowrap;">审核说明：</label>
                            <input type="text" class="form-control form-control-sm" id="auditComment" placeholder="输入审核说明..." style="width: 200px;">
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoNextSwitch" onchange="toggleAutoNext()">
                            <label class="form-check-label ms-2" for="autoNextSwitch" style="font-size: 0.875rem; color: var(--gray-600);">
                                自动切换下一个
                            </label>
                        </div>
                    </div>

                    <!-- 右侧：动态按钮区域 -->
                    <div class="footer-right">
                        <div class="d-flex gap-2" id="actionButtons">
                            <!-- 审核按钮 - 仅在待审核状态显示 -->
                            <button type="button" class="btn btn-success audit-buttons" onclick="submitAudit('pass')" style="display: none;">
                                <i class="bi bi-check-circle me-1"></i>审核通过
                            </button>
                            <button type="button" class="btn btn-danger audit-buttons" onclick="submitAudit('fail')" style="display: none;">
                                <i class="bi bi-x-circle me-1"></i>审核不通过
                            </button>

                            <!-- 复核按钮 - 仅在待复核状态显示 -->
                            <button type="button" class="btn btn-success review-buttons" onclick="submitReview('pass')" style="display: none;">
                                <i class="bi bi-shield-check me-1"></i>复核通过
                            </button>
                            <button type="button" class="btn btn-danger review-buttons" onclick="submitReview('fail')" style="display: none;">
                                <i class="bi bi-shield-x me-1"></i>复核不通过
                            </button>

                            <!-- 导航按钮 - 始终显示 -->
                            <button type="button" class="btn btn-primary" onclick="goToPreviousFile()">
                                <i class="bi bi-arrow-left me-1"></i>上一个文件
                            </button>
                            <button type="button" class="btn btn-primary" onclick="goToNextFile()">
                                <i class="bi bi-arrow-right me-1"></i>下一个文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<style>
/* JSON字段样式 */
.json-field {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border-left: 3px solid var(--primary-color);
}

.json-field-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.json-field-value {
    color: var(--gray-900);
    word-break: break-word;
}

.editable-field {
    background: white;
    border: 1px solid var(--gray-300);
    color: var(--gray-900);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    width: 100%;
    min-height: 2rem;
    resize: vertical;
}

.editable-field:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 0.1rem rgba(99, 102, 241, 0.1);
}

.comparison-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
}

.comparison-match {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.comparison-diff {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.accuracy-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.accuracy-bar {
    width: 60px;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.accuracy-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.accuracy-excellent { background: linear-gradient(90deg, var(--success-color) 0%, #10b981 100%); }
.accuracy-good { background: linear-gradient(90deg, var(--info-color) 0%, #0891b2 100%); }
.accuracy-fair { background: linear-gradient(90deg, var(--warning-color) 0%, #d97706 100%); }
.accuracy-poor { background: linear-gradient(90deg, var(--danger-color) 0%, #dc2626 100%); }

.file-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 分页容器美化 */
#paginationContainer {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-top: 1rem;
}

/* 分页信息区域 */
.pagination-info-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

#paginationInfo {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
}

/* 每页条数选择器 */
.per-page-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 0.375rem 0.75rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.per-page-selector span {
    color: #64748b;
    font-size: 0.8rem;
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
}

.per-page-selector select {
    border: none;
    background: transparent;
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0;
    width: auto;
    min-width: 60px;
}

.per-page-selector select:focus {
    outline: none;
    box-shadow: none;
}

/* 分页按钮组 */
.pagination {
    background: white;
    padding: 0.25rem;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin: 0;
}

.pagination .page-item .page-link {
    background: transparent;
    border: none;
    color: #64748b;
    margin: 0 0.125rem;
    border-radius: 6px;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.pagination .page-item .page-link:hover {
    background: #f1f5f9;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.active .page-link:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    #paginationContainer {
        padding: 0.5rem 1rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .pagination-info-section {
        justify-content: center;
        gap: 0.75rem;
    }

    .pagination {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let currentPage = 1;
    let totalPages = 1;
    let totalItems = 0;
    let perPage = 20;
    let selectedFiles = new Set();
    let autoNextEnabled = true;
    let currentRecordId = null;
    let pendingReviewFiles = [];
    let currentReviewIndex = 0;
    let currentFileList = []; // 存储当前页面的文件列表



    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // 恢复每页显示条数设置
            restorePerPageSetting();

            loadFileList();
            initEventListeners();

            // 恢复自动切换状态
            restoreAutoNextState();

            // 将测试函数暴露到全局作用域
            window.testFieldRendering = testFieldRendering;
            window.testModalWithRealData = testModalWithRealData;
            window.testModalWithBadData = testModalWithBadData;
            window.testStatsOnly = testStatsOnly;
        } catch (error) {
            console.error('页面初始化时出错:', error);
        }
    });

    // 恢复每页显示条数设置
    function restorePerPageSetting() {
        const savedPerPage = localStorage.getItem('globalPerPageSetting');
        if (savedPerPage) {
            perPage = parseInt(savedPerPage);
            const perPageSelect = document.getElementById('perPageSelect');
            if (perPageSelect) {
                perPageSelect.value = perPage.toString();
            }
        }
    }

    // 恢复自动切换状态
    function restoreAutoNextState() {
        const savedState = localStorage.getItem('autoNextEnabled');
        if (savedState !== null) {
            autoNextEnabled = savedState === 'true';
            const switchElement = document.getElementById('autoNextSwitch');
            if (switchElement) {
                switchElement.checked = autoNextEnabled;
            }
        }
    }

    // 初始化事件监听器
    function initEventListeners() {
        try {
            // 搜索框实时搜索
            const searchInput = document.getElementById('fileSearchInput');
            if (searchInput) {
                searchInput.addEventListener('input', debounce(searchFiles, 500));
            } else {
                console.error('搜索框元素未找到');
            }

            // 全屏模态框关闭事件
            const fullscreenModal = document.getElementById('fullscreenModal');
            if (fullscreenModal) {
                // 全屏模态框关闭事件（如果需要其他处理可以在这里添加）
            } else {
                console.error('全屏模态框元素未找到');
            }
        } catch (error) {
            console.error('初始化事件监听器时出错:', error);
        }
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 加载文件列表
    function loadFileList(page = 1) {
        currentPage = page;
        showLoading('正在加载文件列表...');

        const fileStatusFilter = document.getElementById('fileStatusFilter').value;

        const params = {
            page: page,
            per_page: perPage,
            analysis_type: document.getElementById('analysisTypeFilter').value,
            status: document.getElementById('statusFilter').value,
            search: document.getElementById('fileSearchInput').value.trim()
        };

        // 根据文件状态筛选设置参数
        if (fileStatusFilter === 'all') {
            params.include_deprecated = true; // 包含所有文件
        } else if (fileStatusFilter === 'active') {
            params.file_status = 'active'; // 只显示正常文件
        } else if (fileStatusFilter === 'deprecated') {
            params.file_status = 'deprecated'; // 只显示废弃文件
        }

        // 移除空参数
        Object.keys(params).forEach(key => {
            if (!params[key]) delete params[key];
        });

        fetch(`/api/files?${new URLSearchParams(params)}`)
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    renderFileList(data.data.files);
                    updatePagination(data.data.pagination);
                } else {
                    showMessage('加载文件列表失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('加载文件列表失败:', error);
                showMessage('加载文件列表失败', 'error');
            });
    }

    // 渲染文件列表
    function renderFileList(files) {
        // 保存当前文件列表到全局变量
        currentFileList = files;

        const tbody = document.getElementById('fileTableBody');

        if (files.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <i class="bi bi-folder2-open display-4 text-muted"></i>
                        <div class="mt-3 text-muted">暂无文件记录</div>
                        <div class="text-muted">请上传文件后重试</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = '';
        files.forEach(file => {
            const row = createFileRow(file);
            tbody.appendChild(row);
        });

        // 更新全选状态
        updateSelectAllState();
    }

    // 创建文件行
    function createFileRow(file) {
        const tr = document.createElement('tr');
        tr.dataset.fileId = file.id;

        // 如果文件已废弃，添加样式
        if (file.file_status === 'deprecated') {
            tr.classList.add('file-deprecated');
        }

        const statusClass = getStatusClass(file.status);
        const statusText = getStatusText(file.status);
        const analysisTypeName = getAnalysisTypeName(file.analysis_type);

        // 更健壮的准确率处理 - 与文档分析页面一致
        let accuracyScore = '0.0';
        if (file.accuracy_score !== undefined && file.accuracy_score !== null && !isNaN(file.accuracy_score)) {
            // 如果accuracy_score已经是百分比形式（>1），直接使用；否则乘以100
            const rawScore = parseFloat(file.accuracy_score);
            accuracyScore = rawScore > 1 ? rawScore.toFixed(1) : (rawScore * 100).toFixed(1);
        }

        const accuracyClass = getAccuracyClass(file.accuracy_score);

        // 调试信息 - 文件管理页面准确率计算
        // 文件操作按钮逻辑
        const isDeprecated = file.file_status === 'deprecated';
        const canAnalyze = file.file_status === 'active'; // 待审核和待复核状态也可以重分析
        const hasResult = ['completed', 'pending_audit', 'pending_review'].includes(file.status);

        // 移除审核和复核按钮逻辑，这些功能只在查看结果弹窗中提供

        tr.innerHTML = `
            <td>
                <input type="checkbox" class="tech-checkbox file-checkbox"
                       ${canAnalyze && !isDeprecated ? '' : 'disabled'}
                       data-file-id="${file.id}"
                       onchange="handleFileSelection(this)">
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text me-2 text-warning"></i>
                    <span title="${file.filename}">${truncateText(file.filename, 30)}</span>
                </div>
            </td>
            <td>
                <span class="analysis-type-badge analysis-type-${file.analysis_type}">
                    ${analysisTypeName}
                </span>
            </td>
            <td>
                <span class="status-badge ${statusClass}">
                    ${getStatusIcon(file.status)}
                    ${statusText}
                </span>
            </td>
            <td>
                <div class="accuracy-indicator">
                    <span class="small">${accuracyScore}%</span>
                    <div class="accuracy-bar">
                        <div class="accuracy-fill ${accuracyClass}"
                             style="width: ${accuracyScore}%"></div>
                    </div>
                </div>
            </td>
            <td>
                <span class="small text-muted">${formatDateTime(file.created_at)}</span>
            </td>
            <td>
                <div class="file-actions">
                    <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                            onclick="analyzeFile(${file.id})"
                            ${canAnalyze && !isDeprecated ? '' : 'disabled'}>
                        <i class="bi bi-cpu"></i>
                        ${hasResult ? '重分析' : '分析'}
                    </button>
                    <button class="btn btn-view-result btn-sm"
                            onclick="viewResult(${file.id})"
                            ${hasResult ? '' : 'disabled'}>
                        <i class="bi bi-eye"></i>
                        查看结果
                    </button>

                    ${!isDeprecated ? `
                        <button class="btn btn-deprecate btn-sm"
                                onclick="deprecateFile(${file.id})">
                            <i class="bi bi-archive"></i>
                            废弃
                        </button>
                    ` : `
                        <button class="btn btn-restore btn-sm"
                                onclick="restoreFile(${file.id})">
                            <i class="bi bi-arrow-counterclockwise"></i>
                            恢复
                        </button>
                    `}
                </div>
            </td>
        `;

        return tr;
    }

    // 工具函数
    function getStatusClass(status) {
        const classes = {
            'pending': 'status-pending',
            'processing': 'status-processing',
            'analyzing': 'status-processing',
            'completed': 'status-completed',
            'analyzed': 'status-completed',
            'failed': 'status-failed',
            'uploaded': 'status-uploaded',
            'pending_audit': 'status-pending-audit',
            'pending_review': 'status-pending-review',
            'audit_rejected': 'status-failed',
            'review_rejected': 'status-failed',
            'deprecated': 'status-deprecated'
        };
        return classes[status] || 'status-pending';
    }

    function getStatusText(status) {
        const texts = {
            'pending': '待分析',
            'processing': '分析中',
            'analyzing': '分析中',
            'completed': '已完成',
            'analyzed': '已完成',
            'failed': '分析失败',
            'uploaded': '已上传',
            'pending_audit': '待审核',
            'pending_review': '待复核',
            'audit_rejected': '审核不通过',
            'review_rejected': '复核不通过',
            'deprecated': '已废弃'
        };
        return texts[status] || status;
    }

    function getStatusIcon(status) {
        const icons = {
            'pending': '<i class="bi bi-clock"></i>',
            'processing': '<i class="bi bi-hourglass-split"></i>',
            'analyzing': '<i class="bi bi-hourglass-split"></i>',
            'completed': '<i class="bi bi-check-circle"></i>',
            'analyzed': '<i class="bi bi-check-circle"></i>',
            'failed': '<i class="bi bi-x-circle"></i>',
            'uploaded': '<i class="bi bi-upload"></i>',
            'pending_audit': '<i class="bi bi-eye"></i>',
            'pending_review': '<i class="bi bi-shield-check"></i>',
            'audit_rejected': '<i class="bi bi-x-circle"></i>',
            'review_rejected': '<i class="bi bi-x-circle"></i>',
            'deprecated': '<i class="bi bi-archive"></i>'
        };
        return icons[status] || '<i class="bi bi-question-circle"></i>';
    }

    function getAnalysisTypeName(type) {
        // 文件列表中显示简洁的四字名称
        const names = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return names[type] || type;
    }

    function getAnalysisTypeIcon(type) {
        const icons = {
            'futures_account': '<i class="bi bi-graph-up"></i>',
            'wealth_management': '<i class="bi bi-piggy-bank"></i>',
            'broker_interest': '<i class="bi bi-percent"></i>',
            'account_opening': '<i class="bi bi-person-plus"></i>',
            'ningyin_fee': '<i class="bi bi-receipt"></i>',
            'non_standard_trade': '<i class="bi bi-arrow-left-right"></i>'
        };
        return icons[type] || '<i class="bi bi-file-text"></i>';
    }

    function getAccuracyClass(score) {
        if (!score) return 'accuracy-poor';
        if (score >= 0.95) return 'accuracy-excellent';
        if (score >= 0.8) return 'accuracy-good';
        if (score >= 0.6) return 'accuracy-fair';
        return 'accuracy-poor';
    }

    function truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    function formatDateTime(dateStr) {
        if (!dateStr) return '/';

        // 处理多种日期格式
        let date;
        if (typeof dateStr === 'string') {
            // 处理常见的日期格式
            if (dateStr.includes('T')) {
                // ISO格式：2025-08-12T08:24:11.244643
                date = new Date(dateStr);
            } else if (dateStr.includes('-')) {
                // 标准格式：2025-08-12 16:24:11
                date = new Date(dateStr);
            } else {
                // 其他格式
                date = new Date(dateStr);
            }
        } else {
            date = new Date(dateStr);
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            console.warn('无效的日期格式:', dateStr);
            return '/';
        }

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 分页相关函数
    function updatePagination(pagination) {
        if (!pagination) return;

        totalPages = pagination.pages;
        totalItems = pagination.total;
        currentPage = pagination.page;

        // 更新分页信息
        document.getElementById('paginationInfo').textContent = 
            `第 ${currentPage} 页，共 ${totalPages} 页，总计 ${totalItems} 条记录`;

        // 更新分页按钮
        const paginationList = document.getElementById('paginationList');
        paginationList.innerHTML = '';

        if (totalPages <= 1) {
            document.getElementById('paginationContainer').style.display = 'none';
            return;
        }

        document.getElementById('paginationContainer').style.display = 'flex';

        // 上一页按钮
        if (currentPage > 1) {
            paginationList.appendChild(createPageButton(currentPage - 1, '‹', false));
        }

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationList.appendChild(createPageButton(1, '1', false));
            if (startPage > 2) {
                const li = document.createElement('li');
                li.className = 'page-item disabled';
                li.innerHTML = '<span class="page-link">...</span>';
                paginationList.appendChild(li);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationList.appendChild(createPageButton(i, i.toString(), i === currentPage));
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const li = document.createElement('li');
                li.className = 'page-item disabled';
                li.innerHTML = '<span class="page-link">...</span>';
                paginationList.appendChild(li);
            }
            paginationList.appendChild(createPageButton(totalPages, totalPages.toString(), false));
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            paginationList.appendChild(createPageButton(currentPage + 1, '›', false));
        }
    }

    function createPageButton(page, text, isActive) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;
        
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.onclick = (e) => {
            e.preventDefault();
            if (!isActive) {
                loadFileList(page);
            }
        };
        
        li.appendChild(a);
        return li;
    }

    // 搜索和筛选
    function searchFiles() {
        loadFileList(1);
    }

    function applyFilters() {
        loadFileList(1);
    }

    function refreshFileList() {
        selectedFiles.clear();
        loadFileList(currentPage);
    }

    // 改变每页显示条数
    function changePerPage() {
        const perPageSelect = document.getElementById('perPageSelect');
        perPage = parseInt(perPageSelect.value);

        // 保存用户偏好到本地存储（使用全局键名，与文档分析页面同步）
        localStorage.setItem('globalPerPageSetting', perPage);

        // 重新加载第一页
        loadFileList(1);
    }

    // 跳转到文档分析页面
    function goToDocumentAnalysis() {
        window.location.href = '{{ url_for("main.document_analysis") }}';
    }

    // 文件选择相关
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const fileCheckboxes = document.querySelectorAll('.file-checkbox:not(:disabled)');
        
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
            handleFileSelection(checkbox);
        });
    }

    function handleFileSelection(checkbox) {
        const fileId = parseInt(checkbox.dataset.fileId);
        
        if (checkbox.checked) {
            selectedFiles.add(fileId);
        } else {
            selectedFiles.delete(fileId);
        }

        updateSelectAllState();
    }

    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const fileCheckboxes = document.querySelectorAll('.file-checkbox:not(:disabled)');
        const checkedCheckboxes = document.querySelectorAll('.file-checkbox:not(:disabled):checked');

        if (fileCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCheckboxes.length === fileCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else if (checkedCheckboxes.length > 0) {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        } else {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        }
    }

    // 文件操作 - 使用与文档分析页面相同的成功实现
    function analyzeFile(fileId) {
        // 从当前文件列表中查找对应的文件记录
        const fileRecord = currentFileList.find(f => f.id == fileId);
        if (!fileRecord) {
            showMessage('未找到文件记录', 'error');
            return;
        }

        // 检查是否为重分析
        const fileRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
        if (!fileRow) {
            showMessage('未找到文件行', 'error');
            return;
        }

        const statusCell = fileRow.querySelector('.file-status');
        const currentStatus = statusCell ? statusCell.textContent.trim() : '';
        const hasResult = currentStatus === '已分析' || currentStatus === '待复核' || currentStatus === '已复核';
        const actionText = hasResult ? '重分析' : '分析';

        if (hasResult && !confirm('确定要重新分析这个文件吗？这将重新执行AI分析。')) {
            return;
        }

        // 更新UI状态为分析中
        if (statusCell) {
            statusCell.innerHTML = '<span class="badge bg-warning">分析中</span>';
        }

        // 禁用分析按钮并根据是否为重分析显示不同的图标
        const analyzeBtn = fileRow.querySelector('.btn-analyze, .btn-reanalyze');
        if (analyzeBtn) {
            analyzeBtn.disabled = true;
            if (hasResult) {
                // 重分析中：显示重分析样式的按钮
                analyzeBtn.innerHTML = '<i class="bi bi-arrow-clockwise spinning"></i> 分析中...';
                analyzeBtn.className = 'btn btn-reanalyze btn-sm';
            } else {
                // 首次分析中：显示分析样式的按钮
                analyzeBtn.innerHTML = '<i class="bi bi-cpu spinning"></i> 分析中...';
                analyzeBtn.className = 'btn btn-analyze btn-sm';
            }
        }

        // 获取分析类型 - 与文档分析页面保持一致的逻辑
        let analysisType = null;

        // 优先从当前文件记录中获取分析类型
        if (fileRecord && fileRecord.analysis_type) {
            analysisType = fileRecord.analysis_type;
            console.log(`✅ 从文件记录中获取分析类型: ${analysisType}`);
        }

        // 如果没有找到，使用当前选择的分析类型（如果不为空）
        if (!analysisType && window.selectedAnalysisType) {
            analysisType = window.selectedAnalysisType;
            console.log(`⚠️  使用当前选择的分析类型: ${analysisType}`);
        }

        // 如果还是没有，报错并退出
        if (!analysisType) {
            console.error(`❌ 无法确定分析类型 - fileId: ${fileId}, selectedAnalysisType: ${window.selectedAnalysisType}`);
            showMessage('无法确定文件的分析类型，请重新选择分析类型后再试', 'error');

            // 恢复按钮状态
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                if (hasResult) {
                    analyzeBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重分析';
                    analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                } else {
                    analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                    analyzeBtn.className = 'btn btn-analyze btn-sm';
                }
            }

            // 恢复状态显示
            if (statusCell) {
                statusCell.innerHTML = hasResult ? '<span class="badge bg-success">已分析</span>' : '<span class="badge bg-secondary">待分析</span>';
            }
            return;
        }

        console.log(`使用分析类型: ${analysisType}`);

        // 立即更新UI状态为分析中 - 与文档分析页面保持一致
        updateLocalFileAccuracy(fileId, undefined, 'analyzing');

        showMessage(`正在${actionText}文件...`, 'info');

        // 使用与文档分析页面相同的API调用方式
        fetch(`/api/files/${fileId}/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                analysis_type: analysisType,
                enable_seal_recognition: false    // 禁用印章识别，避免处理失败
            })
        })
        .then(response => response.json())
        .then(data => {

            if (data.success) {
                // 更新本地缓存状态 - 与文档分析页面保持一致
                const returnedAccuracy = data?.data?.accuracy_score;
                if (returnedAccuracy !== undefined && returnedAccuracy !== null && !isNaN(returnedAccuracy)) {
                    console.log(`✅ 直接使用API返回的accuracy_score: ${returnedAccuracy}`);
                    updateLocalFileAccuracy(fileId, returnedAccuracy, data.data.status);
                } else {
                    console.log(`⚠️  API未返回accuracy_score，设置状态为已分析`);
                    updateLocalFileAccuracy(fileId, null, 'analyzed');
                }

                // 更新状态为已分析
                if (statusCell) {
                    statusCell.innerHTML = '<span class="badge bg-success">已分析</span>';
                }

                // 恢复按钮状态 - 分析成功后显示重分析按钮
                if (analyzeBtn) {
                    analyzeBtn.disabled = false;
                    analyzeBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重分析';
                    analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                }

                showMessage(`文件${actionText}完成`, 'success');

                // 重新加载列表以确保数据一致性
                loadFileList(currentPage);
            } else {
                // 恢复原状态 - 与文档分析页面保持一致
                updateLocalFileAccuracy(fileId, null, hasResult ? 'analyzed' : 'pending');

                // 更新状态为失败
                if (statusCell) {
                    statusCell.innerHTML = '<span class="badge bg-danger">分析失败</span>';
                }

                // 恢复按钮状态 - 根据原来是否有结果决定显示分析还是重分析
                if (analyzeBtn) {
                    analyzeBtn.disabled = false;
                    if (hasResult) {
                        analyzeBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重分析';
                        analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                    } else {
                        analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                        analyzeBtn.className = 'btn btn-analyze btn-sm';
                    }
                }

                showMessage(`文件${actionText}失败: ${data.message || '未知错误'}`, 'error');
            }
        })
        .catch(error => {
            console.error('文件分析失败:', error);

            // 恢复原状态 - 与文档分析页面保持一致
            updateLocalFileAccuracy(fileId, null, hasResult ? 'analyzed' : 'pending');

            // 更新状态为失败
            if (statusCell) {
                statusCell.innerHTML = '<span class="badge bg-danger">分析失败</span>';
            }

            // 恢复按钮状态 - 根据原来是否有结果决定显示分析还是重分析
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                if (hasResult) {
                    analyzeBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重分析';
                    analyzeBtn.className = 'btn btn-reanalyze btn-sm';
                } else {
                    analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 分析';
                    analyzeBtn.className = 'btn btn-analyze btn-sm';
                }
            }

            showMessage(`${actionText}文件失败，请重试`, 'error');
        });
    }

    // 更新本地文件缓存中的accuracy_score和状态 - 适配文件管理页面
    function updateLocalFileAccuracy(fileId, accuracyScore, status) {
        console.log(`🔄 更新本地文件缓存 - fileId: ${fileId}, accuracyScore: ${accuracyScore}, status: ${status}`);

        let fileFound = false;

        // 更新currentFileList中的数据（文件管理页面的主要数据源）
        if (currentFileList && Array.isArray(currentFileList)) {
            const file = currentFileList.find(f => f.id == fileId || f.fileId == fileId);
            if (file) {
                console.log(`✅ 找到文件 ${fileId} 在当前文件列表中，更新前accuracy_score: ${file.accuracy_score}`);
                if (accuracyScore !== undefined) {
                    file.accuracy_score = accuracyScore;
                }
                if (status) file.status = status;
                console.log(`✅ 更新后accuracy_score: ${file.accuracy_score}`);
                fileFound = true;
            }
        }

        // 如果有filesByType变量（兼容性处理）
        if (typeof filesByType !== 'undefined') {
            for (const [type, files] of Object.entries(filesByType)) {
                const file = files.find(f => f.fileId == fileId || f.id == fileId);
                if (file) {
                    console.log(`✅ 找到文件 ${fileId} 在类型 ${type} 中，更新前accuracy_score: ${file.accuracy_score}`);
                    if (accuracyScore !== undefined) {
                        file.accuracy_score = accuracyScore;
                    }
                    if (status) file.status = status;
                    console.log(`✅ 更新后accuracy_score: ${file.accuracy_score}`);
                    fileFound = true;
                    break;
                }
            }
        }

        if (fileFound) {
            console.log(`✅ 本地缓存更新成功`);
        } else {
            console.warn(`⚠️  未在任何缓存中找到fileId: ${fileId}`);
        }
    }

    function deprecateFile(fileId) {
        if (!confirm('确定要废弃这个文件吗？废弃后该记录将在原位置置灰显示，除查看结果和恢复按钮外，其他操作将不可用。')) {
            return;
        }

        showLoading('正在废弃文件...');

        fetch(`/api/files/${fileId}/deprecate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reason: '用户手动废弃'
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage('文件已废弃，记录已置灰', 'success');
                // 重新加载列表以确保数据一致性，废弃的记录会以灰色背景显示
                loadFileList(currentPage);
            } else {
                showMessage('废弃文件失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('废弃文件失败:', error);
            showMessage('废弃文件失败', 'error');
        });
    }

    // 恢复文件
    function restoreFile(fileId) {
        if (!confirm('确定要恢复这个文件吗？恢复后记录将重新可用，可以进行正常操作。')) {
            return;
        }

        showLoading('正在恢复文件...');

        fetch(`/api/files/${fileId}/restore`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reason: '用户手动恢复'
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage('文件已恢复，记录重新可用', 'success');
                // 重新加载列表以确保数据一致性，恢复的记录会显示正常背景
                loadFileList(currentPage);
            } else {
                showMessage('恢复文件失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('恢复文件失败:', error);
            showMessage('恢复文件失败', 'error');
        });
    }

    // 查看结果
    function viewResult(fileId) {
        currentRecordId = fileId;
        showLoading('正在加载分析结果...');

        fetch(`/api/files/${fileId}/result`)
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showResultModal(data.data);
                } else {
                    showMessage('加载分析结果失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('加载分析结果失败:', error);
                showMessage('加载分析结果失败', 'error');
            });
    }

    // 显示结果模态框
    function showResultModal(resultData) {
        // 设置文件名
        document.getElementById('modalFileName').textContent = resultData.filename;

        // 更新统计信息
        updateStatsSection(resultData);

        // 使用新的字段结构化渲染
        renderFieldStructuredContent(resultData);

        // 渲染原件展示
        renderOriginalFile(resultData);



        // 设置审核评论
        document.getElementById('auditComment').value = resultData.audit_comment || resultData.review_comment || '';

        // 根据文件状态显示相应的按钮
        updateActionButtons(resultData.status);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('resultModal'));
        modal.show();
    }

    // 计算对象中的字段数量（递归）- 与文档分析页面保持一致
    function countFieldsInObject(obj, maxDepth = 3, currentDepth = 0) {
        if (!obj || typeof obj !== 'object' || currentDepth >= maxDepth) {
            return 0;
        }

        let count = 0;

        if (Array.isArray(obj)) {
            // 数组：计算所有元素的字段数
            for (const item of obj) {
                if (typeof item === 'object' && item !== null) {
                    count += countFieldsInObject(item, maxDepth, currentDepth + 1);
                } else {
                    count += 1; // 基本类型也算一个字段
                }
            }
        } else {
            // 对象：计算所有属性
            for (const [key, value] of Object.entries(obj)) {
                if (typeof value === 'object' && value !== null) {
                    // 嵌套对象或数组
                    const nestedCount = countFieldsInObject(value, maxDepth, currentDepth + 1);
                    count += nestedCount > 0 ? nestedCount : 1; // 至少算作1个字段
                } else {
                    // 基本类型字段
                    count += 1;
                }
            }
        }

        return count;
    }

    // 更新统计信息区域
    function updateStatsSection(resultData) {
        // 初始化默认值
        let analysisTypeName = '未知类型';
        let fileAccuracy = 0;
        let fieldAccuracy = 0;
        let correctFields = 0;
        let totalFields = 0;

        // 1. 分析类型名称
        if (resultData.analysis_type_name) {
            analysisTypeName = resultData.analysis_type_name;
        } else if (resultData.analysis_type) {
            // 如果没有显示名称，使用标准映射 - 只支持标准类型
            const typeNames = {
                'futures_account': '期货账户/开户文件解析',
                'wealth_management': '理财产品说明书',
                'broker_interest': '券商账户计息变更',
                'account_opening': '账户开户场景',
                'ningyin_fee': '宁银理财费用变更',
                'non_standard_trade': '非标交易确认单解析'
            };
            analysisTypeName = typeNames[resultData.analysis_type] || resultData.analysis_type;
        }

        // 2. 优先使用API返回的统计信息
        if (resultData.stats) {
            fileAccuracy = resultData.stats.file_accuracy_rate || 0;
            fieldAccuracy = resultData.stats.field_accuracy_rate || 0;
            correctFields = resultData.stats.correct_fields || 0;
            totalFields = resultData.stats.total_fields || 0;
        } else {
            // 3. 兜底逻辑：从其他字段获取
            // 文件正确率
            if (resultData.accuracy_score !== undefined && resultData.accuracy_score !== null) {
                fileAccuracy = resultData.accuracy_score;
            }

            // 字段正确率
            if (resultData.field_accuracy !== undefined && resultData.field_accuracy !== null) {
                if (typeof resultData.field_accuracy === 'number' && !isNaN(resultData.field_accuracy)) {
                    fieldAccuracy = resultData.field_accuracy;
                } else if (typeof resultData.field_accuracy === 'object') {
                    fieldAccuracy = resultData.field_accuracy.accuracy_score ||
                                   resultData.field_accuracy.overall_accuracy ||
                                   resultData.field_accuracy.match_rate ||
                                   resultData.field_accuracy.accuracy || 0;
                }
            } else if (resultData.overall_accuracy !== undefined && resultData.overall_accuracy !== null) {
                fieldAccuracy = resultData.overall_accuracy;
            }

            // 4. 解析对比结果获取字段统计（仅在没有stats时使用）
            if (resultData.comparison_result) {
                try {
                    const comparisonData = typeof resultData.comparison_result === 'string'
                        ? JSON.parse(resultData.comparison_result)
                        : resultData.comparison_result;
                    // 尝试从不同的数据结构中获取字段统计
                    if (comparisonData.total_fields !== undefined && comparisonData.matched_fields !== undefined) {
                        // 直接从根级别获取
                        totalFields = comparisonData.total_fields || 0;
                        correctFields = comparisonData.matched_fields || 0;
                    } else if (comparisonData.field_comparison) {
                        totalFields = comparisonData.field_comparison.total_fields || 0;
                        correctFields = comparisonData.field_comparison.correct_fields || 0;
                    } else if (comparisonData.summary) {
                        totalFields = comparisonData.summary.total_fields || 0;
                        correctFields = comparisonData.summary.correct_fields || 0;
                    } else if (comparisonData.statistics) {
                        totalFields = comparisonData.statistics.total_fields || 0;
                        correctFields = comparisonData.statistics.correct_fields || 0;
                    } else {
                        // 如果没有直接的统计信息，尝试从字段详情中计算
                        if (comparisonData.field_comparisons && Array.isArray(comparisonData.field_comparisons)) {
                            totalFields = comparisonData.field_comparisons.length;
                            correctFields = comparisonData.field_comparisons.filter(field => field.match === true || field.is_match === true).length;
                        } else if (comparisonData.fields && Array.isArray(comparisonData.fields)) {
                            totalFields = comparisonData.fields.length;
                            correctFields = comparisonData.fields.filter(field => field.match === true || field.is_match === true).length;
                        } else if (comparisonData.field_details && Array.isArray(comparisonData.field_details)) {
                            totalFields = comparisonData.field_details.length;
                            correctFields = comparisonData.field_details.filter(field => field.match === true || field.is_match === true).length;
                        }
                    }

                    // 如果comparison_result中包含准确率信息，优先使用
                    if (comparisonData.accuracy_score !== undefined && !isNaN(comparisonData.accuracy_score)) {
                        fieldAccuracy = comparisonData.accuracy_score;
                    }

                    // 如果还是没有获取到，尝试根据准确率反推
                    if (totalFields === 0 && fieldAccuracy > 0 && correctFields > 0) {
                        totalFields = Math.round(correctFields / fieldAccuracy);
                    }
                } catch (e) {
                    console.warn('解析对比结果失败:', e);
                }
            }
        }

        // 3. 如果从对比结果中没有获取到，尝试从其他字段获取
        if (totalFields === 0) {
            totalFields = resultData.total_fields || resultData.field_count || 0;
        }
        if (correctFields === 0) {
            correctFields = resultData.correct_fields || resultData.matched_fields || 0;
        }

        // 4. 如果没有预期结果，尝试从AI结果中计算基本统计信息
        if (totalFields === 0 && correctFields === 0) {
            try {
                const aiResult = resultData.ai_result ?
                    (typeof resultData.ai_result === 'string' ? JSON.parse(resultData.ai_result) : resultData.ai_result) : null;
                const expectedResult = resultData.expected_result ?
                    (typeof resultData.expected_result === 'string' ? JSON.parse(resultData.expected_result) : resultData.expected_result) : null;

                if (aiResult && !expectedResult) {
                    // 没有预期结果时，计算AI结果的字段数量
                    const aiFieldCount = countFieldsInObject(aiResult);
                    totalFields = aiFieldCount;
                    // 没有预期结果时，无法计算正确字段数，显示为待评估
                    correctFields = 0;
                    fieldAccuracy = 0; // 设为0表示待评估
                } else if (aiResult && expectedResult) {
                    // 有预期结果时，如果前面没有获取到统计信息，尝试简单计算
                    const aiFieldCount = countFieldsInObject(aiResult);
                    const expectedFieldCount = countFieldsInObject(expectedResult);
                    totalFields = Math.max(aiFieldCount, expectedFieldCount);
                }
            } catch (e) {
                console.warn('计算AI结果字段数量失败:', e);
            }
        }

        // 5. 兜底逻辑：如果有准确率但没有字段数，尝试反推
        if (totalFields === 0 && fieldAccuracy > 0 && !isNaN(fieldAccuracy)) {
            // 假设有一些字段，根据准确率反推
            if (correctFields > 0) {
                totalFields = Math.round(correctFields / fieldAccuracy);
            } else {
                // 如果连正确字段数都没有，给一个合理的默认值
                totalFields = 10; // 假设有10个字段
                correctFields = Math.round(totalFields * fieldAccuracy);
            }
        }

        // 6. 特殊处理：如果字段准确率是NaN，但有具体的字段统计，重新计算准确率
        if ((isNaN(fieldAccuracy) || fieldAccuracy === 0) && totalFields > 0 && correctFields > 0) {
            fieldAccuracy = correctFields / totalFields;
        }

        // 7. 确保数值的合理性
        fileAccuracy = Math.max(0, Math.min(1, fileAccuracy || 0));
        fieldAccuracy = Math.max(0, Math.min(1, fieldAccuracy || 0));
        correctFields = Math.max(0, correctFields || 0);
        totalFields = Math.max(0, totalFields || 0);

        // 更新显示
        document.getElementById('fileAccuracy').textContent = analysisTypeName;

        // 字段正确率显示逻辑 - 与文档分析页面保持一致
        if (fieldAccuracy > 0) {
            document.getElementById('fieldAccuracy').textContent = `${(fieldAccuracy * 100).toFixed(1)}%`;
        } else if (totalFields > 0 && correctFields === 0) {
            // 有总字段数但没有正确字段数，说明没有预期结果
            document.getElementById('fieldAccuracy').textContent = '待评估';
        } else if (isNaN(fieldAccuracy)) {
            document.getElementById('fieldAccuracy').textContent = 'NaN%';
        } else {
            document.getElementById('fieldAccuracy').textContent = '-';
        }

        // 正确字段数显示逻辑 - 与文档分析页面保持一致
        if (correctFields > 0) {
            document.getElementById('correctFields').textContent = correctFields;
        } else if (totalFields > 0) {
            // 有总字段数但没有正确字段数，说明没有预期结果
            document.getElementById('correctFields').textContent = '待评估';
        } else {
            document.getElementById('correctFields').textContent = '-';
        }

        // 总字段数显示
        document.getElementById('totalFields').textContent = totalFields > 0 ? totalFields : '-';
    }

    // 合并记录原件文件切换相关变量 (全局变量)
    window.currentMergedFiles = [];
    window.currentFileIndex = 0;
    window.isShowingMergedFiles = false;

    // 渲染原件展示
    function renderOriginalFile(resultData) {
        // 设置全局变量
        currentRecordId = resultData.id;

        // 调试信息
        console.log('🔍 renderOriginalFile 调试信息:', {
            id: resultData.id,
            filename: resultData.filename,
            analysis_type: resultData.analysis_type,
            status: resultData.status
        });

        // 检查是否是账户开户场景，如果是则尝试加载合并文件
        if (resultData.analysis_type === 'account_opening') {
            console.log('✅ 检测到账户开户场景，尝试加载合并文件');
            loadMergedFiles(resultData);
        } else {
            console.log('ℹ️ 非账户开户场景，使用单文件模式');
            renderSingleOriginalFile(resultData);
        }
    }

    // 渲染单个原件文件
    function renderSingleOriginalFile(resultData) {
        const container = document.getElementById('originalFileContent');
        if (!container) {
            console.error('找不到原件展示容器');
            return;
        }

        // 隐藏文件切换控件
        hideFileSelectionControls();

        // 清空现有内容
        container.innerHTML = '';

        // 检查是否有文件信息
        const hasFileInfo = resultData.file_info && resultData.file_info.upload_path;
        const fileName = resultData.filename || '';
        const fileExtension = fileName.split('.').pop().toLowerCase();

        if (hasFileInfo || resultData.file_path || resultData.original_file_url) {
            // 显示加载指示器
            container.innerHTML = `
                <div class="text-center text-muted">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载原件文件...</p>
                </div>
            `;

            // 使用新的API获取文件数据
            fetch(`/api/files/${currentRecordId}/original-data`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const fileData = data.data;
                        // 根据文件类型显示不同内容
                        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileData.file_type.replace('.', ''))) {
                            // 图片文件
                            const img = document.createElement('img');
                            img.src = fileData.data_url;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'contain';
                            img.style.display = 'block';
                            img.alt = '原件图片';
                            img.onload = function() {
                                // 图片加载完成
                            };
                            img.onerror = function() {
                                console.error('图片加载失败');
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                        <p class="mt-2">图片加载失败</p>
                                        <small class="text-muted">文件: ${fileName}</small>
                                    </div>
                                `;
                            };
                            container.innerHTML = '';
                            container.appendChild(img);
                        } else if (fileData.file_type === '.pdf') {
                            // PDF文件 - 使用直接文件URL避免Base64大小限制
                            const iframe = document.createElement('iframe');
                            iframe.src = `/api/files/${currentRecordId}/original`;

                            iframe.style.width = '100%';
                            iframe.style.height = '100%';
                            iframe.style.border = 'none';
                            iframe.style.minHeight = '500px';

                            iframe.onload = function() {
                                // PDF加载完成
                            };

                            iframe.onerror = function() {
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: var(--danger-color);"></i>
                                        <p class="mt-2">PDF预览不可用</p>
                                        <small class="text-muted mb-2 d-block">文件: ${fileName}</small>
                                        <a href="/api/files/${currentRecordId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-download me-1"></i>下载查看
                                        </a>
                                    </div>
                                `;
                            };

                            container.innerHTML = '';
                            container.appendChild(iframe);
                        } else if (fileData.file_type === '.xlsx' || fileData.file_type === '.xls' ||
                                   fileData.file_type === '.docx' || fileData.file_type === '.doc') {
                            // Office文件 - 使用预览API
                            // 显示加载指示器
                            container.innerHTML = `
                                <div class="text-center text-muted">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">转换中...</span>
                                    </div>
                                    <p class="mt-2">正在转换文件预览...</p>
                                </div>
                            `;

                            // 调用预览API
                            fetch(`/api/files/${currentRecordId}/preview`)
                                .then(response => response.json())
                                .then(previewData => {
                                    if (previewData.success) {
                                        // 创建预览容器
                                        const previewDiv = document.createElement('div');
                                        previewDiv.className = 'office-preview-container';
                                        previewDiv.style.width = '100%';
                                        previewDiv.style.height = '100%';
                                        previewDiv.style.overflow = 'auto';
                                        previewDiv.style.padding = '10px';
                                        previewDiv.style.backgroundColor = '#fff';
                                        previewDiv.innerHTML = previewData.data.content;

                                        container.innerHTML = '';
                                        container.appendChild(previewDiv);
                                    } else {
                                        console.error('Office文件预览失败:', previewData.message);
                                        container.innerHTML = `
                                            <div class="text-center text-muted">
                                                <i class="bi bi-file-earmark-excel" style="font-size: 3rem; color: var(--warning-color);"></i>
                                                <p class="mt-2">预览转换失败</p>
                                                <small class="text-muted mb-2 d-block">文件: ${fileName}</small>
                                                <small class="text-muted mb-2 d-block">${previewData.message}</small>
                                                <a href="/api/files/${currentRecordId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-download me-1"></i>下载查看
                                                </a>
                                            </div>
                                        `;
                                    }
                                })
                                .catch(error => {
                                    console.error('Office文件预览请求失败:', error);
                                    container.innerHTML = `
                                        <div class="text-center text-muted">
                                            <i class="bi bi-file-earmark-excel" style="font-size: 3rem; color: var(--danger-color);"></i>
                                            <p class="mt-2">预览加载失败</p>
                                            <small class="text-muted mb-2 d-block">文件: ${fileName}</small>
                                            <a href="/api/files/${currentRecordId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-download me-1"></i>下载查看
                                            </a>
                                        </div>
                                    `;
                                });
                        } else {
                            // 其他文件类型
                            container.innerHTML = `
                                <div class="text-center text-muted">
                                    <i class="bi bi-file-earmark" style="font-size: 3rem; color: var(--info-color);"></i>
                                    <p class="mt-2">${fileName}</p>
                                    <p class="small">不支持预览此文件类型</p>
                                    <a href="/api/files/${currentRecordId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-download me-1"></i>下载文件
                                    </a>
                                </div>
                            `;
                        }
                    } else {
                        console.error('获取文件数据失败:', data.message);
                        container.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                <p class="mt-2">文件加载失败</p>
                                <small class="text-muted">${data.message}</small>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取文件数据异常:', error);
                    container.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--danger-color);"></i>
                            <p class="mt-2">网络错误</p>
                            <small class="text-muted">无法加载原件文件</small>
                        </div>
                    `;
                });
        } else {
            // 没有原件信息
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-file-earmark-x" style="font-size: 3rem; opacity: 0.3;"></i>
                    <p class="mt-2">暂无原件</p>
                    <small class="text-muted">文件信息未保存</small>
                </div>
            `;
        }
    }

    // ===== 合并文件切换功能 =====

    // 加载合并文件数据
    function loadMergedFiles(resultData) {
        const container = document.getElementById('originalFileContent');

        console.log('🔍 loadMergedFiles 开始，记录ID:', currentRecordId);

        // 显示加载指示器
        container.innerHTML = `
            <div class="text-center text-muted">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在检查合并文件...</p>
            </div>
        `;

        // 尝试获取合并文件数据
        const apiUrl = `/api/files/${currentRecordId}/merged-files`;
        console.log('🌐 请求API:', apiUrl);

        fetch(apiUrl)
            .then(response => {
                console.log('📡 API响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('📦 API响应数据:', data);

                if (data.success && data.data.files && data.data.files.length > 1) {
                    console.log('✅ 检测到多文件，文件数量:', data.data.files.length);
                    // 多文件情况，显示文件选择功能
                    window.currentMergedFiles = data.data.files;
                    window.currentFileIndex = 0;
                    window.isShowingMergedFiles = true;
                    setupMergedFileDisplay();
                    displayCurrentFile();
                } else {
                    console.log('ℹ️ 单文件或无合并文件，回退到单文件模式');
                    // 单文件情况，使用原有逻辑
                    window.isShowingMergedFiles = false;
                    renderSingleOriginalFile(resultData);
                }
            })
            .catch(error => {
                console.error('❌ 获取合并文件失败:', error);
                // 回退到单文件逻辑
                window.isShowingMergedFiles = false;
                renderSingleOriginalFile(resultData);
            });
    }

    // 设置合并文件显示界面
    function setupMergedFileDisplay() {
        // 显示文件选择控件
        const fileSelectDropdown = document.getElementById('fileSelectDropdown');
        const multiFileIndicator = document.getElementById('multiFileIndicator');

        if (fileSelectDropdown) {
            fileSelectDropdown.style.display = 'block';
            setupFileSelectMenu();
        }

        if (multiFileIndicator) {
            multiFileIndicator.style.display = 'inline-block';
            updateFileIndicator();
        }
    }

    // 隐藏文件选择控件
    function hideFileSelectionControls() {
        const fileSelectDropdown = document.getElementById('fileSelectDropdown');
        const multiFileIndicator = document.getElementById('multiFileIndicator');
        const fileNavigationControls = document.getElementById('fileNavigationControls');

        if (fileSelectDropdown) fileSelectDropdown.style.display = 'none';
        if (multiFileIndicator) multiFileIndicator.style.display = 'none';
        if (fileNavigationControls) fileNavigationControls.style.display = 'none';
    }

    // 设置文件选择列表
    function setupFileSelectMenu() {
        const list = document.getElementById('globalFileSelectList');
        if (!list) return;

        list.innerHTML = '';
        window.currentMergedFiles.forEach((file, index) => {
            const item = document.createElement('div');
            item.className = `d-flex align-items-center p-3 border-bottom ${index === window.currentFileIndex ? 'bg-primary text-white' : ''}`;
            item.style.cursor = 'pointer';
            item.style.borderRadius = '6px';
            item.style.marginBottom = '4px';
            item.style.transition = 'all 0.2s ease';

            if (index !== window.currentFileIndex) {
                item.onmouseenter = () => {
                    item.style.backgroundColor = '#f8f9fa';
                    item.style.transform = 'translateX(4px)';
                };
                item.onmouseleave = () => {
                    item.style.backgroundColor = '';
                    item.style.transform = 'translateX(0)';
                };
            }

            item.innerHTML = `
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark${file.is_main ? '-check' : ''} me-2 ${index === window.currentFileIndex ? 'text-white' : 'text-primary'}"></i>
                        <span class="fw-medium">${file.display_name}</span>
                    </div>
                    ${file.is_main ? `<small class="text-muted ${index === window.currentFileIndex ? 'text-white-50' : ''}">主文件</small>` : ''}
                </div>
                ${index === window.currentFileIndex ? '<i class="bi bi-check-circle-fill text-white"></i>' : '<i class="bi bi-chevron-right text-muted"></i>'}
            `;
            item.onclick = () => {
                switchToFile(index);
                hideFileSelector(); // 关闭面板
            };
            list.appendChild(item);
        });
    }

    // 切换文件选择器显示
    function toggleFileSelector() {
        const panel = document.getElementById('globalFileSelectorPanel');
        const button = document.getElementById('fileSelectorBtn');

        if (!panel || !button) return;

        if (panel.style.display === 'none') {
            // 计算按钮位置
            const buttonRect = button.getBoundingClientRect();
            const panelWidth = 280;

            // 设置面板位置
            panel.style.left = Math.max(10, Math.min(buttonRect.left, window.innerWidth - panelWidth - 10)) + 'px';
            panel.style.top = (buttonRect.bottom + 8) + 'px';

            // 如果面板会超出屏幕底部，则显示在按钮上方
            const panelHeight = 400; // 最大高度
            if (buttonRect.bottom + panelHeight + 20 > window.innerHeight) {
                panel.style.top = (buttonRect.top - panelHeight - 8) + 'px';
            }

            setupFileSelectMenu(); // 更新文件列表
            panel.style.display = 'block';

            // 添加动画效果
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                panel.style.transition = 'all 0.2s ease';
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            }, 10);

            // 点击外部关闭面板
            setTimeout(() => {
                document.addEventListener('click', closeFileSelectorOnClickOutside);
            }, 100);
        } else {
            hideFileSelector();
        }
    }

    // 隐藏文件选择器
    function hideFileSelector() {
        const panel = document.getElementById('globalFileSelectorPanel');
        if (panel) {
            panel.style.transition = 'all 0.2s ease';
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                panel.style.display = 'none';
            }, 200);
            document.removeEventListener('click', closeFileSelectorOnClickOutside);
        }
    }

    // 点击外部关闭文件选择器
    function closeFileSelectorOnClickOutside(event) {
        const panel = document.getElementById('globalFileSelectorPanel');
        const button = document.getElementById('fileSelectorBtn');

        if (panel && button &&
            !panel.contains(event.target) &&
            !button.contains(event.target)) {
            hideFileSelector();
        }
    }

    // 更新文件指示器
    function updateFileIndicator() {
        const currentIndexElement = document.getElementById('currentFileIndex');
        const totalCountElement = document.getElementById('totalFileCount');

        if (currentIndexElement) {
            currentIndexElement.textContent = window.currentFileIndex + 1;
        }
        if (totalCountElement) {
            totalCountElement.textContent = window.currentMergedFiles.length;
        }
    }

    // 显示当前文件
    function displayCurrentFile() {
        if (!window.currentMergedFiles || window.currentMergedFiles.length === 0) return;

        const currentFile = window.currentMergedFiles[window.currentFileIndex];

        // 渲染该文件的原件
        renderOriginalFileContent(currentFile.id);

        // 更新文件选择菜单的活动状态
        setupFileSelectMenu();

        // 更新文件指示器
        updateFileIndicator();
    }

    // 渲染原件文件内容
    function renderOriginalFileContent(fileId) {
        const container = document.getElementById('originalFileContent');
        const fileName = window.currentMergedFiles[window.currentFileIndex]?.filename || '';

        console.log(`🔍 开始加载文件ID: ${fileId}, 文件名: ${fileName}`);

        // 显示加载状态
        container.innerHTML = `
            <div class="text-center text-muted">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载原件...</p>
                <small class="text-muted">文件ID: ${fileId}</small>
            </div>
        `;

        try {
            // 使用API获取文件数据
            fetch(`/api/files/${fileId}/original-data`)
                .then(response => {
                    console.log(`📡 API响应状态: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`📋 API响应数据:`, data);
                    if (data.success) {
                        const fileData = data.data;
                        // 根据文件类型显示不同内容
                        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileData.file_type.replace('.', ''))) {
                            // 图片文件
                            const img = document.createElement('img');
                            img.src = fileData.data_url;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'contain';
                            img.style.display = 'block';
                            img.alt = '原件图片';
                            img.onload = function() {
                                // 图片加载完成
                            };
                            img.onerror = function() {
                                console.error('图片加载失败');
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                        <p class="mt-2">图片加载失败</p>
                                        <small class="text-muted">文件: ${fileName}</small>
                                    </div>
                                `;
                            };
                            container.innerHTML = '';
                            container.appendChild(img);
                        } else if (fileData.file_type === '.pdf') {
                            // PDF文件
                            const iframe = document.createElement('iframe');
                            iframe.src = `/api/files/${fileId}/original`;
                            iframe.style.width = '100%';
                            iframe.style.height = '100%';
                            iframe.style.border = 'none';
                            iframe.style.minHeight = '500px';
                            iframe.onload = function() {
                                // PDF加载完成
                            };
                            iframe.onerror = function() {
                                container.innerHTML = `
                                    <div class="text-center text-muted">
                                        <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: var(--danger-color);"></i>
                                        <p class="mt-2">PDF预览不可用</p>
                                        <small class="text-muted mb-2 d-block">文件: ${fileName}</small>
                                        <a href="/api/files/${fileId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-download me-1"></i>下载查看
                                        </a>
                                    </div>
                                `;
                            };
                            container.innerHTML = '';
                            container.appendChild(iframe);
                        } else {
                            // 其他文件类型
                            container.innerHTML = `
                                <div class="text-center text-muted">
                                    <i class="bi bi-file-earmark" style="font-size: 3rem; color: var(--info-color);"></i>
                                    <p class="mt-2">${fileName}</p>
                                    <p class="small">不支持预览此文件类型</p>
                                    <a href="/api/files/${fileId}/original" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-download me-1"></i>下载文件
                                    </a>
                                </div>
                            `;
                        }
                    } else {
                        container.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--warning-color);"></i>
                                <p class="mt-2">文件加载失败</p>
                                <small class="text-muted">${data.message}</small>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取文件数据异常:', error);
                    container.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--danger-color);"></i>
                            <p class="mt-2">网络错误</p>
                            <small class="text-muted">无法加载原件文件</small>
                        </div>
                    `;
                });
        } catch (error) {
            console.error('渲染原件时发生错误:', error);
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-bug" style="font-size: 3rem; color: var(--danger-color);"></i>
                    <p class="mt-2">渲染错误</p>
                    <p class="small">请刷新页面后重试</p>
                </div>
            `;
        }
    }

    // 文件切换函数
    function switchToFile(index) {
        if (index >= 0 && index < window.currentMergedFiles.length) {
            window.currentFileIndex = index;
            displayCurrentFile();
            updateFileIndicator();
        }
    }

    // 切换到上一个文件
    function switchToPreviousFile() {
        if (window.currentFileIndex > 0) {
            switchToFile(window.currentFileIndex - 1);
        }
    }

    // 切换到下一个文件
    function switchToNextFile() {
        if (window.currentFileIndex < window.currentMergedFiles.length - 1) {
            switchToFile(window.currentFileIndex + 1);
        }
    }

    // 提交审核结果（从弹窗）
    function submitAudit(status) {
        const comment = document.getElementById('auditComment').value.trim();

        // 验证当前记录ID
        if (!currentRecordId) {
            showMessage('错误：未找到当前文件记录', 'error');
            return;
        }

        if (!confirm(`确定要${status === 'pass' ? '通过' : '不通过'}这个文件的审核吗？`)) {
            return;
        }

        showLoading('正在提交审核结果...');

        // 构建请求数据
        const requestData = {
            audit_status: status,
            audit_comment: comment
        };
        fetch(`/api/files/${currentRecordId}/audit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();

            if (data.success) {
                showMessage(`审核${status === 'pass' ? '通过' : '不通过'}成功`, 'success');

                // 关闭弹窗
                const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
                if (modal) {
                    modal.hide();
                }

                // 如果开启了自动切换，切换到下一个
                if (autoNextEnabled) {
                    setTimeout(() => {
                        findNextFileInCurrentList();
                    }, 500);
                } else {
                    // 刷新文件列表
                    setTimeout(() => {
                        loadFileList(currentPage);
                    }, 500);
                }
            } else {
                showMessage('审核失败: ' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('审核失败:', error);
            showMessage('审核失败: ' + error.message, 'error');
        });
    }

    // 提交复核结果（从弹窗）
    function submitReview(status) {
        const comment = document.getElementById('auditComment').value.trim();

        // 验证当前记录ID
        if (!currentRecordId) {
            showMessage('错误：未找到当前文件记录', 'error');
            return;
        }

        if (!confirm(`确定要${status === 'pass' ? '通过' : '不通过'}这个文件的复核吗？`)) {
            return;
        }

        showLoading('正在提交复核结果...');

        // 构建请求数据
        const requestData = {
            review_status: status,
            review_comment: comment
        };
        fetch(`/api/files/${currentRecordId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();

            if (data.success) {
                const statusText = status === 'pass' ? '通过，文件已完成' : '不通过';
                showMessage(`复核${statusText}`, 'success');

                // 关闭弹窗
                const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
                if (modal) {
                    modal.hide();
                }

                // 如果开启了自动切换，切换到下一个
                if (autoNextEnabled) {
                    setTimeout(() => {
                        findNextFileInCurrentList();
                    }, 500);
                } else {
                    // 刷新文件列表
                    setTimeout(() => {
                        loadFileList(currentPage);
                    }, 500);
                }
            } else {
                showMessage('复核失败: ' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('复核失败:', error);
            showMessage('复核失败: ' + error.message, 'error');
        });
    }

    // 根据文件状态更新操作按钮显示
    function updateActionButtons(status) {
        const auditButtons = document.querySelectorAll('.audit-buttons');
        const reviewButtons = document.querySelectorAll('.review-buttons');

        // 隐藏所有按钮
        auditButtons.forEach(btn => btn.style.display = 'none');
        reviewButtons.forEach(btn => btn.style.display = 'none');

        // 根据状态显示相应按钮
        if (status === 'pending_audit') {
            // 待审核状态：显示审核按钮
            auditButtons.forEach(btn => btn.style.display = 'inline-block');
        } else if (status === 'pending_review') {
            // 待复核状态：显示复核按钮
            reviewButtons.forEach(btn => btn.style.display = 'inline-block');
        }
        // 其他状态（completed, audit_rejected, review_rejected等）不显示操作按钮
    }

    // 一键自动审核功能
    function autoAuditFiles() {
        if (!confirm('确定要自动审核所有100%识别率的待审核文件吗？')) {
            return;
        }

        showLoading('正在查找符合条件的文件...');

        // 获取所有待审核的文件
        fetch('/api/files/pending-audit')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    // 筛选出100%识别率的文件
                    const perfectFiles = data.data.filter(file =>
                        file.accuracy_score && parseFloat(file.accuracy_score) >= 1.0
                    );

                    if (perfectFiles.length === 0) {
                        hideLoading();
                        showMessage('没有找到100%识别率的待审核文件', 'info');
                        return;
                    }
                    // 批量审核这些文件
                    batchAuditFiles(perfectFiles);
                } else {
                    hideLoading();
                    showMessage('没有找到待审核的文件', 'info');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('获取待审核文件失败:', error);
                showMessage('获取待审核文件失败', 'error');
            });
    }

    // 批量审核文件
    function batchAuditFiles(files) {
        let completedCount = 0;
        let successCount = 0;
        let failedCount = 0;

        const updateProgress = () => {
            const progress = Math.round((completedCount / files.length) * 100);
            showLoading(`正在自动审核... (${completedCount}/${files.length}) ${progress}%`);
        };

        const processFile = (file, index) => {
            return fetch(`/api/files/${file.id}/audit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    audit_status: 'pass',
                    audit_comment: '一键自动审核通过（100%识别率）'
                })
            })
            .then(response => response.json())
            .then(data => {
                completedCount++;
                if (data.success) {
                    successCount++;
                } else {
                    failedCount++;
                    console.error(`文件 ${file.filename} 审核失败:`, data.message);
                }
                updateProgress();
            })
            .catch(error => {
                completedCount++;
                failedCount++;
                console.error(`文件 ${file.filename} 审核失败:`, error);
                updateProgress();
            });
        };

        // 并发处理，但限制并发数量
        const batchSize = 3; // 每次最多处理3个文件
        const processBatch = async (startIndex) => {
            const batch = files.slice(startIndex, startIndex + batchSize);
            const promises = batch.map((file, index) => processFile(file, startIndex + index));
            await Promise.all(promises);

            if (startIndex + batchSize < files.length) {
                await processBatch(startIndex + batchSize);
            }
        };

        processBatch(0).then(() => {
            hideLoading();

            if (successCount > 0) {
                showMessage(`自动审核完成！成功审核${successCount}个文件，${failedCount}个失败`, 'success');

                // 刷新文件列表
                setTimeout(() => {
                    loadFileList(currentPage);
                }, 1000);
            } else {
                showMessage('自动审核失败，没有文件被成功审核', 'error');
            }
        });
    }

    // 获取文件类型对应的字段结构定义
    function getFieldStructureByType(analysisType) {
        // 安全检查
        if (!analysisType || typeof analysisType !== 'string') {
            console.warn('分析类型无效:', analysisType);
            return null;
        }

        // 类型映射：将实际的分析类型映射到字段结构定义
        const typeMapping = {
            'futures_account': 'FUTURES_ACCOUNT',
            'wealth_management': 'WEALTH_PRODUCT',
            'non_standard_trade': 'NON_STANDARD_TRADE',
            'broker_interest': 'BROKER_INTEREST',
            'ningyin_fee': 'NINGBO_FEE',
            'account_opening': 'ACCOUNT_OPENING'
        };

        const mappedType = typeMapping[analysisType] || analysisType.toUpperCase();
        const fieldStructures = {
            'FUTURES_ACCOUNT': {
                name: '期货账户文件解析',
                fields: [
                    { name: '产品名称', path: '产品名称', type: 'String', group: '基本信息' },
                    { name: '资金账号', path: '资金账号', type: 'String', group: '基本信息' },
                    { name: '会员号_上期所', path: '会员号.上期所', type: 'String', group: '会员号信息' },
                    { name: '会员号_大商所', path: '会员号.大商所', type: 'String', group: '会员号信息' },
                    { name: '会员号_郑商所', path: '会员号.郑商所', type: 'String', group: '会员号信息' },
                    { name: '会员号_中金所', path: '会员号.中金所', type: 'String', group: '会员号信息' },
                    { name: '会员号_上能所', path: '会员号.上能所', type: 'String', group: '会员号信息' },
                    { name: '会员号_广期所', path: '会员号.广期所', type: 'String', group: '会员号信息' },
                    { name: '交易编码_上期所_投机', path: '交易编码.上期所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上期所_套利', path: '交易编码.上期所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上期所_套保', path: '交易编码.上期所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_大商所_投机', path: '交易编码.大商所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_大商所_套利', path: '交易编码.大商所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_大商所_套保', path: '交易编码.大商所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_郑商所_投机', path: '交易编码.郑商所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_郑商所_套利', path: '交易编码.郑商所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_郑商所_套保', path: '交易编码.郑商所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_中金所_投机', path: '交易编码.中金所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_中金所_套利', path: '交易编码.中金所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_中金所_套保', path: '交易编码.中金所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上能所_投机', path: '交易编码.上能所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上能所_套利', path: '交易编码.上能所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_上能所_套保', path: '交易编码.上能所.套保', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_广期所_投机', path: '交易编码.广期所.投机', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_广期所_套利', path: '交易编码.广期所.套利', type: 'String', group: '交易编码信息' },
                    { name: '交易编码_广期所_套保', path: '交易编码.广期所.套保', type: 'String', group: '交易编码信息' },
                    { name: '开始时间', path: '开始时间', type: 'Date', group: '时间信息' },
                    { name: '结束时间', path: '结束时间', type: 'String', group: '时间信息' }
                ]
            },
            'WEALTH_PRODUCT': {
                name: '理财产品说明书',
                fields: [], // 动态生成字段
                isDynamic: true // 标记为动态字段类型
            },
            'NON_STANDARD_TRADE': {
                name: '非标交易确认单',
                fields: [
                    { name: '投资者名称', path: '投资者名称', type: 'String', group: '投资者信息' },
                    { name: '投资者账号', path: '投资者账号', type: 'String', group: '投资者信息' },
                    { name: '业务日期', path: '业务日期', type: 'String', group: '交易信息' },
                    { name: '业务类型', path: '业务类型', type: 'String', group: '交易信息' },
                    { name: '投资标的名称', path: '投资标的名称', type: 'String', group: '投资标的信息' },
                    { name: '投资标的代码', path: '投资标的代码', type: 'String', group: '投资标的信息' },
                    { name: '投资标的金额', path: '投资标的金额', type: 'Decimal', group: '投资标的信息' },
                    { name: '投资标的数量', path: '投资标的数量', type: 'Decimal', group: '投资标的信息' },
                    { name: '交易费用', path: '交易费用', type: 'String', group: '交易信息' }
                ]
            },
            'BROKER_INTEREST': {
                name: '券商账户计息变更',
                fields: [], // 动态生成字段
                isDynamic: true // 标记为动态字段类型
            },
            'NINGBO_FEE': {
                name: '宁银费用变更',
                fields: [], // 动态生成字段
                isDynamic: true // 标记为动态字段类型
            },
            'ACCOUNT_OPENING': {
                name: '账户开户场景解析 V1.2',
                fields: [
                    // 管理机构信息
                    { name: '管理机构名称', path: 'manager_info.name', type: 'String', group: '管理机构信息' },
                    { name: '管理机构地址', path: 'manager_info.address', type: 'String', group: '管理机构信息' },
                    { name: '管理机构联系方式', path: 'manager_info.contact', type: 'String', group: '管理机构信息' },

                    // 投资者信息
                    { name: '投资者名称', path: 'investor_info.name', type: 'String', group: '投资者信息' },
                    { name: '投资者类型', path: 'investor_info.type', type: 'String', group: '投资者信息' },
                    { name: '账户性质', path: 'investor_info.account_nature', type: 'String', group: '投资者信息' },

                    // 联系人信息 (数组) - 支持多个联系人
                    { name: '联系人信息', path: 'contact_info', type: 'Array', group: '联系人信息',
                      arrayFields: [
                          { name: '联系人', path: 'contact_person', type: 'String' },
                          { name: '联系电话', path: 'phone', type: 'String' }
                      ]
                    },

                    // 检测结果
                    { name: '印章完整性', path: 'seal_integrity', type: 'String', group: '检测结果' },
                    { name: '页面连续性', path: 'page_continuity', type: 'String', group: '检测结果' }
                ]
            }
        };

        return fieldStructures[mappedType] || null;
    }

    // 解析JSON数据
    function parseJSONData(data) {
        if (!data) return null;
        if (typeof data === 'string') {
            try {
                return JSON.parse(data);
            } catch (e) {
                console.error('JSON解析失败:', e);
                return null;
            }
        }
        return data;
    }

    // 产品类型中文翻译映射
    const productTypeTranslations = {
        'single_product': '单一产品',
        'company_wide_product': '公司级产品',
        'multiple_products': '多产品',
        'portfolio_product': '组合产品'
    };

    // 翻译产品类型
    function translateProductType(value) {
        if (!value) return value;
        return productTypeTranslations[value] || value;
    }

    // 数字转中文大写数字
    function numberToChinese(num) {
        const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
        if (num <= 10) {
            return chineseNumbers[num];
        } else if (num < 20) {
            return '十' + chineseNumbers[num - 10];
        } else if (num < 100) {
            const tens = Math.floor(num / 10);
            const ones = num % 10;
            return chineseNumbers[tens] + '十' + (ones > 0 ? chineseNumbers[ones] : '');
        }
        return num.toString(); // 超过100的数字保持原样
    }

    // 格式化非个人利率字段名称
    function formatNonIndividualFieldName(subField, productIndex) {
        // 解析时间段格式，如 "START:2024-11-11", "2024-11-11:END", "START:2024-11-01"
        if (subField.includes('START:') && subField.includes('END')) {
            // 格式如 "START:2024-11-11:END" 或 "2024-11-11:2024-12-31"
            const parts = subField.split(':');
            if (parts.length >= 3) {
                const startDate = parts[1];
                const endDate = parts[2] === 'END' ? '至今' : parts[2];
                return `非个人利率(${startDate}至${endDate})`;
            }
        } else if (subField.startsWith('START:')) {
            // 格式如 "START:2024-11-11"
            const startDate = subField.replace('START:', '');
            return `非个人利率(${startDate}起)`;
        } else if (subField.endsWith(':END')) {
            // 格式如 "2024-11-11:END"
            const endDate = subField.replace(':END', '');
            return `非个人利率(至${endDate})`;
        } else if (subField.includes(':')) {
            // 格式如 "2024-11-01:2024-11-10"
            const [startDate, endDate] = subField.split(':');
            return `非个人利率(${startDate}至${endDate})`;
        } else {
            // 其他格式，保持原样但更友好
            return `非个人利率_${subField}`;
        }
    }

    // 根据路径获取嵌套对象的值
    function getValueByPath(obj, path) {
        if (!obj || !path) return null;

        const keys = path.split('.');
        let current = obj;

        for (const key of keys) {
            if (current === null || current === undefined) return null;
            if (Array.isArray(current) && !isNaN(key)) {
                current = current[parseInt(key)];
            } else {
                current = current[key];
            }
        }

        return current;
    }

    // 根据字段路径和值进行特殊处理
    function processFieldValue(fieldPath, value) {
        if (!value) return value;

        // 如果是产品类型字段，进行翻译
        if (fieldPath && fieldPath.includes('product_type')) {
            return translateProductType(value);
        }

        // 印章完整性字段的中文映射
        if (fieldPath && fieldPath.includes('seal_integrity')) {
            const sealIntegrityMap = {
                'complete': '完整',
                'incomplete': '不完整',
                'unknown': '未知'
            };
            return sealIntegrityMap[value] || value;
        }

        // 页面连续性字段的中文映射
        if (fieldPath && fieldPath.includes('page_continuity')) {
            const pageContinuityMap = {
                'continuous': '连续',
                'discontinuous': '不连续',
                'unknown': '未知'
            };
            return pageContinuityMap[value] || value;
        }

        return value;
    }

    // 渲染字段结构化内容
    function renderFieldStructuredContent(resultData) {
        const fieldStructure = getFieldStructureByType(resultData.analysis_type);

        if (!fieldStructure) {
            // 如果没有字段结构定义，使用原来的JSON渲染方式
            renderJSONContent('aiResultContent', resultData.ai_result, false);
            renderJSONContent('expectedResultContent', resultData.expected_result, true);
            renderComparisonResult('comparisonResultContent', resultData.comparison_result);
            document.getElementById('fieldNamesContent').innerHTML = '<div class="text-muted">暂无字段结构定义<br>分析类型: ' + resultData.analysis_type + '</div>';
            return;
        }

        // 检查数据是否为数组类型
        const aiData = parseJSONData(resultData.ai_result);
        const expectedData = parseJSONData(resultData.expected_result);

        // 检查是否为动态字段类型（如理财产品说明书）
        if (fieldStructure.isDynamic) {
            console.log('检测到动态字段类型:', fieldStructure.name);
            // 动态生成字段
            const dynamicFields = generateDynamicFields(fieldStructure, resultData.ai_result, resultData.expected_result);
            console.log('动态生成的字段数量:', dynamicFields.length);
            renderFourColumnsMergedGroups(dynamicFields, resultData.ai_result, resultData.expected_result);
            return;
        }

        // 检查是否包含数组字段（通过字段结构定义或实际数据类型）
        const hasArrayFields = fieldStructure.fields && fieldStructure.fields.some(field => field.type === 'Array' && field.arrayFields);
        if (hasArrayFields || fieldStructure.isArray || Array.isArray(aiData) || Array.isArray(expectedData)) {
            renderArrayFieldContentMerged(fieldStructure, aiData, expectedData, resultData.comparison_result);
            return;
        }
        // 使用新的四列合并分组渲染方法
        renderFourColumnsMergedGroups(fieldStructure.fields, resultData.ai_result, resultData.expected_result);
    }

    // 原件展示区域展开/收起功能
    let isOriginalFileCollapsed = false;

    function toggleOriginalFile() {
        const container = document.getElementById('resultColumnsContainer');
        const originalArea = document.getElementById('originalFileArea');
        const toggleBtn = document.getElementById('toggleOriginalBtn');
        const expandBtn = document.getElementById('expandOriginalBtn');
        const toggleIcon = document.getElementById('toggleIcon');
        const statusText = document.getElementById('originalFileStatus');

        if (isOriginalFileCollapsed) {
            // 展开原件展示
            container.style.gridTemplateColumns = '1fr 450px';
            originalArea.style.opacity = '0';
            originalArea.style.display = 'flex';

            // 延迟显示内容，确保动画流畅
            setTimeout(() => {
                originalArea.style.opacity = '1';
            }, 50);

            // 显示收起按钮，隐藏展开按钮
            toggleBtn.style.display = 'flex';
            expandBtn.style.display = 'none';

            toggleIcon.className = 'bi bi-chevron-left';
            toggleBtn.title = '收起原件展示';
            if (statusText) statusText.textContent = '展开状态';
            isOriginalFileCollapsed = false;
        } else {
            // 收起原件展示
            originalArea.style.opacity = '0';

            // 延迟隐藏，确保动画完成
            setTimeout(() => {
                container.style.gridTemplateColumns = '1fr 0px';
                originalArea.style.display = 'none';

                // 隐藏收起按钮，显示展开按钮
                toggleBtn.style.display = 'none';
                expandBtn.style.display = 'flex';
            }, 150);

            toggleIcon.className = 'bi bi-chevron-right';
            if (statusText) statusText.textContent = '收起状态';
            isOriginalFileCollapsed = true;
        }
    }

    // 四列合并分组渲染函数
    function renderFourColumnsMergedGroups(fields, aiResult, expectedResult) {
        const container = document.getElementById('fourColumnsContentContainer');
        if (!container) {
            console.error('找不到fourColumnsContentContainer容器');
            return;
        }
        container.innerHTML = '';

        // 解析JSON数据
        const aiData = parseJSONData(aiResult);
        const expectedData = parseJSONData(expectedResult);

        if (!aiData && !expectedData) {
            container.innerHTML = '<div class="text-muted">暂无数据</div>';
            return;
        }

        // 按分组组织字段
        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            const groupFields = groups[groupName];

            // 添加合并的分组标题行
            addMergedGroupHeader(container, groupName);

            // 添加该分组的所有字段行
            groupFields.forEach(field => {
                const fieldPath = field.originalPath || field.originalName || field.path;
                const aiValue = getValueByPath(aiData, fieldPath);
                const expectedValue = getValueByPath(expectedData, fieldPath);

                addFourColumnRow(container, {
                    fieldName: field.displayName || field.name,
                    aiValue: formatFieldValue(aiValue, field.type, fieldPath),
                    expectedValue: formatFieldValue(expectedValue, field.type, fieldPath),
                    comparisonResult: getComparisonIcon(aiValue, expectedValue),
                    fieldPath: fieldPath
                });
            });
        });
    }

    // 五列对齐渲染函数（保留备用）
    function renderFiveColumnsAligned(fields, aiResult, expectedResult) {
        // 获取五个容器
        const groupNamesContainer = document.getElementById('groupNamesContent');
        const fieldNamesContainer = document.getElementById('fieldNamesContent');
        const aiResultContainer = document.getElementById('aiResultContent');
        const expectedResultContainer = document.getElementById('expectedResultContent');
        const comparisonResultContainer = document.getElementById('comparisonResultContent');

        // 清空所有容器
        groupNamesContainer.innerHTML = '';
        fieldNamesContainer.innerHTML = '';
        aiResultContainer.innerHTML = '';
        expectedResultContainer.innerHTML = '';
        comparisonResultContainer.innerHTML = '';

        // 按分组组织字段
        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            const groupFields = groups[groupName];

            // 为第一个字段添加分组标题，其他字段留空
            groupFields.forEach((field, index) => {
                // 获取字段值
                const fieldPath = field.originalPath || field.originalName || field.path;
                const aiValue = getValueByPath(aiResult, fieldPath);
                const expectedValue = getValueByPath(expectedResult, fieldPath);

                // 分组列：只在第一行显示分组名称
                if (index === 0) {
                    addGroupCell(groupNamesContainer, groupName, groupFields.length);
                } else {
                    addEmptyCell(groupNamesContainer);
                }

                // 其他四列正常显示
                const fieldDisplayName = field.displayName || field.name;
                addFieldRow(fieldNamesContainer, fieldDisplayName, 'field-name', false, null, fieldDisplayName);
                addFieldRow(aiResultContainer, formatFieldValue(aiValue, field.type), 'ai-value', false, null, fieldDisplayName);
                addFieldRow(expectedResultContainer, formatFieldValue(expectedValue, field.type), 'expected-value', true, fieldPath, fieldDisplayName);
                addFieldRow(comparisonResultContainer, getComparisonIcon(aiValue, expectedValue), 'comparison-result', false, null, fieldDisplayName);
            });
        });
    }

    // 四列对齐渲染函数（保留备用）
    function renderFourColumnsAligned(fields, aiResult, expectedResult) {
        // 获取四个容器
        const fieldNamesContainer = document.getElementById('fieldNamesContent');
        const aiResultContainer = document.getElementById('aiResultContent');
        const expectedResultContainer = document.getElementById('expectedResultContent');
        const comparisonResultContainer = document.getElementById('comparisonResultContent');

        // 清空所有容器
        fieldNamesContainer.innerHTML = '';
        aiResultContainer.innerHTML = '';
        expectedResultContainer.innerHTML = '';
        comparisonResultContainer.innerHTML = '';

        // 按分组组织字段
        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            // 为每列添加分组标题
            addGroupHeader(fieldNamesContainer, groupName);
            addGroupHeader(aiResultContainer, groupName);
            addGroupHeader(expectedResultContainer, groupName);
            addGroupHeader(comparisonResultContainer, groupName);

            // 渲染分组内的字段
            groups[groupName].forEach(field => {
                // 获取字段值
                const fieldPath = field.originalPath || field.originalName || field.path;
                const aiValue = getValueByPath(aiResult, fieldPath);
                const expectedValue = getValueByPath(expectedResult, fieldPath);

                // 为每列添加字段行
                const fieldDisplayName = field.displayName || field.name;
                addFieldRow(fieldNamesContainer, fieldDisplayName, 'field-name', false, null, fieldDisplayName);
                addFieldRow(aiResultContainer, formatFieldValue(aiValue, field.type, fieldPath), 'ai-value', false, null, fieldDisplayName);
                addFieldRow(expectedResultContainer, formatFieldValue(expectedValue, field.type, fieldPath), 'expected-value', true, fieldPath, fieldDisplayName);
                addFieldRow(comparisonResultContainer, getComparisonIcon(aiValue, expectedValue), 'comparison-result', false, null, fieldDisplayName);
            });
        });
    }

    // 按分组组织字段（优化版：处理下划线字段名称）
    function groupFieldsByGroup(fields) {
        const groups = {};
        fields.forEach(field => {
            let group = field.group || '其他';
            let fieldName = field.name;

            // 检查是否是券商计息变更的字段，如果是则不进行下划线分组处理
            const isBrokerageField = group.includes('产品') || fieldName.includes('产品名称') || fieldName.includes('产品类型') ||
                                   fieldName.includes('个人利率') || fieldName.includes('非个人利率') || fieldName.includes('利率(年化)') ||
                                   fieldName.includes('开始时间') || fieldName.includes('截止时间') || fieldName.includes('计息天数') || fieldName.includes('备注');

            // 如果字段名包含下划线且不是券商计息变更字段，将最左边的第一级作为分组标题
            if (fieldName.includes('_') && !isBrokerageField) {
                const parts = fieldName.split('_');
                if (parts.length >= 2) {
                    // 使用最左边的第一级作为分组名（蓝色小标题）
                    group = parts[0];
                    // 字段名使用去掉第一级后的剩余部分
                    fieldName = parts.slice(1).join('_');

                    // 创建新的字段对象，避免修改原始字段
                    field = {
                        ...field,
                        name: fieldName,
                        displayName: fieldName, // 保存显示用的名称
                        originalName: field.name, // 保存原始名称用于数据查找
                        originalPath: field.path // 保存原始路径用于数据查找
                    };
                }
            }

            if (!groups[group]) {
                groups[group] = [];
            }
            groups[group].push(field);
        });
        return groups;
    }

    // 添加合并的分组标题行（跨四列）
    function addMergedGroupHeader(container, groupName) {
        const groupRow = document.createElement('div');
        groupRow.className = 'merged-group-header-row';
        groupRow.style.cssText = `
            display: grid;
            grid-template-columns: 1.5fr 2fr 2fr 1fr;
            margin: 0.5rem 0 0.25rem 0;
        `;

        // 创建跨四列的分组标题
        const groupHeader = document.createElement('div');
        groupHeader.className = 'merged-group-header';
        groupHeader.textContent = groupName;
        groupHeader.style.cssText = `
            grid-column: 1 / -1;
            background: linear-gradient(135deg, var(--primary-color), #4f46e5);
            color: white;
            padding: 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin: 0 1rem;
        `;

        groupRow.appendChild(groupHeader);
        container.appendChild(groupRow);
    }

    // 添加四列数据行
    function addFourColumnRow(container, data) {
        // 检查是否是备注字段
        const isRemarksField = (data.fieldName && data.fieldName.includes('备注')) ||
                              (data.fieldPath && data.fieldPath.includes('remarks'));

        const dataRow = document.createElement('div');
        dataRow.className = 'four-column-data-row';
        dataRow.style.cssText = `
            display: grid;
            grid-template-columns: 1.5fr 2fr 2fr 1fr;
            margin: 0.25rem 0;
            align-items: center;
        `;

        // 字段名称列
        const fieldNameCell = document.createElement('div');
        fieldNameCell.className = `field-row-aligned field-name${isRemarksField ? ' remarks-field' : ''}`;
        fieldNameCell.style.cssText = `
            padding: 1rem;
            border-right: 1px solid var(--gray-200);
        `;
        const fieldNameContent = document.createElement('div');
        fieldNameContent.textContent = data.fieldName;
        // 如果是备注字段且内容较长，添加tooltip
        if (isRemarksField && data.fieldName && data.fieldName.length > 20) {
            fieldNameContent.title = data.fieldName;
        }
        fieldNameContent.style.cssText = `
            border: 1px solid #dee2e6;
            background: white;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            display: flex;
            align-items: center;
            font-weight: 500;
            height: 32px;
            ${isRemarksField ? 'overflow: hidden; text-overflow: ellipsis; white-space: nowrap;' : ''}
        `;
        fieldNameCell.appendChild(fieldNameContent);

        // AI识别内容列
        const aiValueCell = document.createElement('div');
        aiValueCell.className = `field-row-aligned ai-value${isRemarksField ? ' remarks-field' : ''}`;
        aiValueCell.style.cssText = `
            padding: 1rem;
            border-right: 1px solid var(--gray-200);
        `;
        const aiValueContent = document.createElement('div');
        aiValueContent.textContent = data.aiValue || '/';
        // 如果是备注字段且内容较长，添加tooltip
        if (isRemarksField && data.aiValue && data.aiValue.length > 30) {
            aiValueContent.title = data.aiValue;
        }
        aiValueContent.style.cssText = `
            border: 1px solid #dee2e6;
            background: white;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            display: flex;
            align-items: center;
            font-family: 'Consolas', 'Monaco', monospace;
            height: 32px;
            ${isRemarksField ? 'overflow: hidden; text-overflow: ellipsis; white-space: nowrap;' : ''}
        `;
        aiValueCell.appendChild(aiValueContent);

        // 预期结果列（可编辑）
        const expectedValueCell = document.createElement('div');
        expectedValueCell.className = `field-row-aligned expected-value${isRemarksField ? ' remarks-field' : ''}`;
        expectedValueCell.style.cssText = `
            padding: 1rem;
            border-right: 1px solid var(--gray-200);
        `;

        // 所有字段都使用input输入框，保持一致性
        const expectedInput = document.createElement('input');
        expectedInput.type = 'text';
        expectedInput.className = 'form-control form-control-sm editable-field';
        expectedInput.value = data.expectedValue || '';
        expectedInput.dataset.path = data.fieldPath;

        if (isRemarksField) {
            // 为 textarea 添加自动调整高度功能
            expectedInput.style.cssText = `
                min-height: 32px;
                height: auto;
                border: 1px solid #dee2e6;
                background: #f8f9ff;
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
                line-height: 1.4;
                border-radius: 4px;
                margin: 3px;
                border-color: #10b981;
                font-family: 'Consolas', 'Monaco', monospace;
                resize: vertical;
                overflow-y: auto;
                word-wrap: break-word;
                white-space: pre-wrap;
            `;

            // 自动调整高度函数
            const autoResize = () => {
                expectedInput.style.height = 'auto';
                expectedInput.style.height = Math.max(32, expectedInput.scrollHeight) + 'px';

                // 同步整行的高度
                const row = expectedInput.closest('.four-column-data-row');
                if (row) {
                    setTimeout(() => {
                        syncRowHeight(row);
                    }, 0);
                }
            };

            expectedInput.addEventListener('input', autoResize);
            expectedInput.addEventListener('focus', autoResize);

            // 初始调整
            setTimeout(autoResize, 0);
        } else {
            expectedInput.style.cssText = `
                height: 32px;
                border: 1px solid #dee2e6;
                background: #f8f9ff;
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
                line-height: 1.4;
                border-radius: 4px;
                margin: 3px;
                border-color: #10b981;
                font-family: 'Consolas', 'Monaco', monospace;
            `;
        }

        expectedValueCell.appendChild(expectedInput);

        // 对比结果列
        const comparisonCell = document.createElement('div');
        comparisonCell.className = `field-row-aligned comparison-result${isRemarksField ? ' remarks-field' : ''}`;
        comparisonCell.style.cssText = `
            padding: 1rem;
        `;
        const comparisonContent = document.createElement('div');
        comparisonContent.textContent = data.comparisonResult;
        comparisonContent.style.cssText = `
            border: 1px solid #dee2e6;
            background: white;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 4px;
            margin: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-family: 'Consolas', 'Monaco', monospace;
            height: 32px;
        `;
        comparisonCell.appendChild(comparisonContent);

        // 添加所有列到行
        dataRow.appendChild(fieldNameCell);
        dataRow.appendChild(aiValueCell);
        dataRow.appendChild(expectedValueCell);
        dataRow.appendChild(comparisonCell);

        container.appendChild(dataRow);

        // 备注字段现在使用固定高度，不需要同步
    }

    // 简化的行高度函数 - 现在所有字段都是固定高度，不需要同步
    function syncRowHeight(row) {
        console.log('All fields now use fixed height, no sync needed');

        // 移除所有高度同步类，确保使用默认样式
        const cells = row.querySelectorAll('.field-row-aligned.height-synced');
        cells.forEach(cell => {
            cell.classList.remove('height-synced');
        });
    }

    // 添加分组单元格（合并多行）
    function addGroupCell(container, groupName, rowCount) {
        const groupCell = document.createElement('div');
        groupCell.className = 'group-cell-merged';
        groupCell.textContent = groupName;
        groupCell.style.cssText = `
            background: linear-gradient(135deg, var(--primary-color), #4f46e5);
            color: white;
            padding: 0.75rem;
            margin: 0.25rem 0;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: ${rowCount * 44}px;
            writing-mode: horizontal-tb;
        `;
        container.appendChild(groupCell);
    }

    // 添加空单元格
    function addEmptyCell(container) {
        const emptyCell = document.createElement('div');
        emptyCell.className = 'empty-cell';
        emptyCell.style.cssText = `
            height: 38px;
            margin: 0.25rem 0;
            background: transparent;
        `;
        container.appendChild(emptyCell);
    }

    // 添加分组标题
    function addGroupHeader(container, groupName) {
        const groupHeader = document.createElement('div');
        groupHeader.className = 'field-group-header-aligned';
        groupHeader.textContent = groupName;
        groupHeader.style.cssText = `
            background: linear-gradient(135deg, var(--primary-color), #4f46e5);
            color: white;
            padding: 0.5rem 0.75rem;
            margin: 0.5rem 0 0.25rem 0;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        `;
        container.appendChild(groupHeader);
    }

    // 添加字段行
    function addFieldRow(container, content, className, isEditable = false, fieldPath = null, fieldName = null) {
        const fieldRow = document.createElement('div');

        // 检查是否是备注字段
        const isRemarksField = (fieldName && fieldName.includes('备注')) ||
                              (fieldPath && fieldPath.includes('remarks')) ||
                              (content && className === 'field-name' && content.toString().includes('备注'));

        // 为备注字段添加特殊CSS类
        fieldRow.className = `field-row-aligned ${className}${isRemarksField ? ' remarks-field' : ''}`;

        // 检查内容长度，决定是否需要自适应高度
        const contentLength = (content || '').toString().length;
        const needsAutoHeight = isRemarksField || contentLength > 50;

        // 统一的字段行样式，基于input的高度
        fieldRow.style.cssText = `
            margin: 0.25rem 0;
            border-radius: 4px;
            border-left: 3px solid var(--primary-color);
            background: var(--gray-50);
            ${needsAutoHeight ? 'min-height: 38px; height: auto;' : 'height: 38px;'}
            display: flex;
            align-items: center;
            word-break: break-word;
            line-height: 1.4;
            position: relative;
        `;

        if (isEditable && fieldPath) {
            // 创建可编辑字段（文本框或文本域）
            if (isRemarksField) {
                // 备注字段使用单行输入框
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control form-control-sm editable-field';
                input.value = content || '';
                input.dataset.path = fieldPath;
                input.style.cssText = `
                    border: 1px solid #dee2e6;
                    background: white;
                    padding: 0.375rem 0.75rem;
                    font-size: 0.875rem;
                    line-height: 1.4;
                    width: 100%;
                    height: 32px;
                    border-radius: 4px;
                    margin: 3px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                `;

                // 添加tooltip显示完整内容
                if (content && content.length > 30) {
                    input.title = content;
                }

                // 当内容改变时更新tooltip
                input.addEventListener('input', function() {
                    if (this.value && this.value.length > 30) {
                        this.title = this.value;
                    } else {
                        this.title = '';
                    }
                });

                fieldRow.appendChild(input);
            } else {
                // 其他字段使用文本框
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control form-control-sm editable-field';
                input.value = content || '';
                input.dataset.path = fieldPath;
                input.style.cssText = `
                    border: 1px solid #dee2e6;
                    background: white;
                    padding: 0.375rem 0.75rem;
                    font-size: 0.875rem;
                    line-height: 1.4;
                    width: 100%;
                    height: 32px;
                    border-radius: 4px;
                    margin: 3px;
                `;
                fieldRow.appendChild(input);
            }
        } else {
            // 创建模拟文本框样式的容器
            const textContainer = document.createElement('div');
            // 所有字段都使用统一的单行样式
            textContainer.style.cssText = `
                border: 1px solid #dee2e6;
                background: white;
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
                line-height: 1.4;
                width: 100%;
                height: 32px;
                border-radius: 4px;
                margin: 3px;
                display: flex;
                align-items: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            `;

            // 如果是备注字段且内容较长，添加tooltip
            if (isRemarksField && content && content.length > 30) {
                textContainer.title = content;
            }
            textContainer.textContent = content || '/';
            fieldRow.appendChild(textContainer);
        }

        container.appendChild(fieldRow);
    }

    // 获取对比图标
    function getComparisonIcon(aiValue, expectedValue) {
        const ai = String(aiValue || '').trim();
        const expected = String(expectedValue || '').trim();

        if (ai === expected) {
            return '✓ 匹配';
        } else {
            return '✗ 不匹配';
        }
    }

    // 渲染字段名称列（带分组）
    function renderFieldNames(containerId, fields) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器元素:', containerId);
            return;
        }
        container.innerHTML = '';

        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            // 创建分组标题
            const groupHeader = document.createElement('div');
            groupHeader.className = 'field-group-header';
            groupHeader.textContent = groupName;
            container.appendChild(groupHeader);

            // 创建分组内容容器
            const groupContent = document.createElement('div');
            groupContent.className = 'field-group-content';

            groups[groupName].forEach(field => {
                const fieldRow = document.createElement('div');
                fieldRow.className = 'field-row';

                const fieldName = document.createElement('div');
                // 使用优化后的字段名称显示逻辑，不再显示嵌套样式
                fieldName.className = 'field-name';
                // 使用displayName或name作为显示文本
                fieldName.textContent = field.displayName || field.name;
                fieldName.title = `类型: ${field.type}\n路径: ${field.path}\n分组: ${groupName}\n原始名称: ${field.originalName || field.name}`;

                fieldRow.appendChild(fieldName);
                groupContent.appendChild(fieldRow);
            });

            container.appendChild(groupContent);
        });
    }

    // 格式化字段值显示
    function formatFieldValue(value, type, fieldPath = null) {
        if (value === null || value === undefined || value === '') {
            return '/';
        }

        let processedValue = value;

        // 如果有字段路径，进行特殊处理（如翻译）
        if (fieldPath) {
            processedValue = processFieldValue(fieldPath, value);
        }

        const strValue = String(processedValue);

        // 处理特殊值
        if (strValue === 'None' || strValue === 'null') {
            return '/';
        }

        // 根据类型格式化
        switch (type) {
            case 'Date':
                // 日期格式化
                if (strValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    return strValue;
                }
                return strValue;

            case 'Decimal':
                // 数字格式化，添加千分位分隔符
                if (!isNaN(strValue) && strValue !== '') {
                    return parseFloat(strValue).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                }
                return strValue;

            case 'Integer':
                // 整数格式化
                if (!isNaN(strValue) && strValue !== '') {
                    return parseInt(strValue).toLocaleString('zh-CN');
                }
                return strValue;

            case 'String':
            default:
                // 字符串处理，备注字段不截断，其他字段截断过长文本
                const isRemarksField = fieldPath && (fieldPath.includes('remarks') || fieldPath.includes('备注'));
                if (!isRemarksField && strValue.length > 50) {
                    return strValue.substring(0, 47) + '...';
                }
                return strValue;
        }
    }

    // 渲染字段值列（带分组）
    function renderFieldValues(containerId, fields, data, valueClass, editable = false) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            // 创建分组标题
            const groupHeader = document.createElement('div');
            groupHeader.className = 'field-group-header';
            groupHeader.textContent = groupName;
            container.appendChild(groupHeader);

            // 创建分组内容容器
            const groupContent = document.createElement('div');
            groupContent.className = 'field-group-content';

            groups[groupName].forEach(field => {
                const fieldRow = document.createElement('div');
                fieldRow.className = 'field-row';

                const fieldValue = document.createElement('div');
                fieldValue.className = `field-value ${valueClass}`;

                // 使用原始路径来获取数据值
                const fieldPath = field.originalPath || field.originalName || field.path;
                const value = getValueByPath(data, fieldPath);
                const displayValue = formatFieldValue(value, field.type, fieldPath);

                if (editable) {
                    const input = document.createElement('textarea');
                    input.className = 'editable-field';
                    input.value = value !== null && value !== undefined ? String(value) : '';
                    input.dataset.fieldPath = field.path;
                    input.style.width = '100%';
                    input.style.minHeight = '2rem';
                    input.style.border = '1px solid var(--gray-300)';
                    input.style.borderRadius = '4px';
                    input.style.padding = '0.25rem';
                    input.style.fontFamily = 'inherit';
                    input.style.fontSize = 'inherit';
                    input.style.resize = 'vertical';

                    // 添加字段类型提示
                    input.title = `字段类型: ${field.type}\n路径: ${field.path}\n分组: ${groupName}`;

                    fieldValue.appendChild(input);
                } else {
                    // 检查是否是备注字段
                    const isRemarksField = field.name && (field.name.includes('备注') || field.path.includes('remarks'));

                    if (isRemarksField) {
                        // 备注字段允许换行显示
                        fieldValue.style.whiteSpace = 'pre-wrap';
                        fieldValue.style.wordBreak = 'break-word';
                        fieldValue.style.maxHeight = 'none';
                        fieldValue.style.overflow = 'visible';
                    }

                    fieldValue.textContent = displayValue;
                    fieldValue.title = `原始值: ${value !== null && value !== undefined ? String(value) : '空值'}\n字段类型: ${field.type}\n分组: ${groupName}`;
                }

                fieldRow.appendChild(fieldValue);
                groupContent.appendChild(fieldRow);
            });

            container.appendChild(groupContent);
        });
    }

    // 渲染字段对比结果列（带分组）
    function renderFieldComparison(containerId, fields, aiData, expectedData) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        const groups = groupFieldsByGroup(fields);

        Object.keys(groups).forEach(groupName => {
            // 创建分组标题
            const groupHeader = document.createElement('div');
            groupHeader.className = 'field-group-header';
            groupHeader.textContent = groupName;
            container.appendChild(groupHeader);

            // 创建分组内容容器
            const groupContent = document.createElement('div');
            groupContent.className = 'field-group-content';

            groups[groupName].forEach(field => {
                const fieldRow = document.createElement('div');
                fieldRow.className = 'field-row';

                // 使用原始路径来获取数据值
                const fieldPath = field.originalPath || field.originalName || field.path;
                const aiValue = getValueByPath(aiData, fieldPath);
                const expectedValue = getValueByPath(expectedData, fieldPath);

                const aiStr = aiValue !== null && aiValue !== undefined ? String(aiValue) : '/';
                const expectedStr = expectedValue !== null && expectedValue !== undefined ? String(expectedValue) : '/';

                const isMatch = aiStr === expectedStr;

                const comparisonStatus = document.createElement('div');
                comparisonStatus.className = `comparison-status ${isMatch ? 'match' : 'mismatch'}`;
                comparisonStatus.innerHTML = `
                    <i class="bi bi-${isMatch ? 'check-circle' : 'x-circle'}"></i>
                    ${isMatch ? '匹配' : '不匹配'}
                `;
                comparisonStatus.title = `AI值: ${aiStr}\n预期值: ${expectedStr}\n字段: ${field.name}`;

                fieldRow.appendChild(comparisonStatus);
                groupContent.appendChild(fieldRow);
            });

            container.appendChild(groupContent);
        });
    }

    // 动态生成字段（根据实际数据）
    function generateDynamicFields(fieldStructure, aiResult, expectedResult) {
        const fields = [];

        try {
            // 解析AI结果和预期结果
            const aiData = parseJSONData(aiResult);
            const expectedData = parseJSONData(expectedResult);

            // 根据字段结构类型处理不同的动态字段生成
            if (fieldStructure.name === '理财产品说明书') {
                // 理财产品说明书：处理销售机构数组
                const aiArray = aiData?.销售机构 || [];
                const expectedArray = expectedData?.销售机构 || [];

                // 取两者中较大的长度
                const maxLength = Math.max(
                    Array.isArray(aiArray) ? aiArray.length : 0,
                    Array.isArray(expectedArray) ? expectedArray.length : 0
                );

                // 如果没有数据，至少显示一个字段
                const finalLength = maxLength > 0 ? maxLength : 1;

                // 动态生成字段
                for (let i = 0; i < finalLength; i++) {
                    fields.push({
                        name: `销售机构 ${i + 1}`, // 从1开始计数，更美观
                        path: `销售机构.${i}`,
                        type: 'String',
                        group: '销售机构'
                    });
                }
            } else if (fieldStructure.name === '券商账户计息变更') {
                // 券商账户计息变更：动态处理产品数组
                const aiArray = Array.isArray(aiData) ? aiData : [];
                const expectedArray = Array.isArray(expectedData) ? expectedData : [];

                // 取两者中较大的长度
                const maxLength = Math.max(aiArray.length, expectedArray.length);

                // 如果没有数据，至少显示一个产品
                const finalLength = maxLength > 0 ? maxLength : 1;

                // 为每个产品动态生成字段
                for (let i = 0; i < finalLength; i++) {
                    const productIndex = i + 1;
                    const aiItem = aiArray[i] || {};
                    const expectedItem = expectedArray[i] || {};

                    // 合并两个数据源的所有字段（包括空值字段）
                    const allFields = new Set();

                    // 收集AI结果中的所有字段（包括空值）
                    Object.keys(aiItem).forEach(key => {
                        allFields.add(key);
                    });

                    // 收集期望结果中的所有字段（包括空值）
                    Object.keys(expectedItem).forEach(key => {
                        allFields.add(key);
                    });

                    // 确保基础字段始终存在（即使数据中没有）
                    const requiredFields = ['product_name', 'product_type', 'interest_rate', 'start_time', 'end_time', 'interest_days', 'remarks'];
                    requiredFields.forEach(field => {
                        allFields.add(field);
                    });

                    // 动态添加基础字段
                    const chineseProductIndex = numberToChinese(productIndex);
                    if (allFields.has('product_name')) {
                        fields.push({ name: `产品名称`, path: `${i}.product_name`, type: 'String', group: `产品 ${chineseProductIndex}` });
                    }
                    if (allFields.has('product_type')) {
                        fields.push({ name: `产品类型`, path: `${i}.product_type`, type: 'String', group: `产品 ${chineseProductIndex}` });
                    }

                    // 动态处理利率字段
                    if (allFields.has('interest_rate')) {
                        const aiInterestRate = aiItem.interest_rate || {};
                        const expectedInterestRate = expectedItem.interest_rate || {};

                        // 收集所有利率字段，但排除父级对象字段
                        const interestFields = new Set();

                        // 检查AI结果的利率字段（包括空值）
                        Object.keys(aiInterestRate).forEach(key => {
                            // 如果是嵌套对象，只收集子字段，不收集父级字段
                            if (typeof aiInterestRate[key] === 'object' && aiInterestRate[key] !== null) {
                                Object.keys(aiInterestRate[key]).forEach(subKey => {
                                    interestFields.add(`${key}.${subKey}`);
                                });
                            } else {
                                // 只有非对象类型的字段才直接添加
                                interestFields.add(key);
                            }
                        });

                        // 检查期望结果的利率字段（包括空值）
                        Object.keys(expectedInterestRate).forEach(key => {
                            // 如果是嵌套对象，只收集子字段，不收集父级字段
                            if (typeof expectedInterestRate[key] === 'object' && expectedInterestRate[key] !== null) {
                                Object.keys(expectedInterestRate[key]).forEach(subKey => {
                                    interestFields.add(`${key}.${subKey}`);
                                });
                            } else {
                                // 只有非对象类型的字段才直接添加
                                interestFields.add(key);
                            }
                        });

                        // 添加利率字段
                        interestFields.forEach(field => {
                            let fieldName = '';
                            let fieldPath = `${i}.interest_rate.${field}`;

                            if (field === 'all') {
                                fieldName = `利率(年化)`;
                            } else if (field === 'individual') {
                                fieldName = `个人利率`;
                            } else if (field.startsWith('non_individual.')) {
                                const subField = field.replace('non_individual.', '');
                                fieldName = formatNonIndividualFieldName(subField, productIndex);
                            } else if (field.startsWith('all.')) {
                                const subField = field.replace('all.', '');
                                fieldName = `利率(年化)_${subField}`;
                            } else {
                                fieldName = `${field}`;
                            }

                            fields.push({ name: fieldName, path: fieldPath, type: 'String', group: `产品 ${chineseProductIndex}` });
                        });
                    }

                    // 动态添加其他字段
                    if (allFields.has('start_time')) {
                        fields.push({ name: `开始时间`, path: `${i}.start_time`, type: 'Date', group: `产品${chineseProductIndex}` });
                    }
                    if (allFields.has('end_time')) {
                        fields.push({ name: `截止时间`, path: `${i}.end_time`, type: 'String', group: `产品${chineseProductIndex}` });
                    }
                    if (allFields.has('interest_days')) {
                        fields.push({ name: `计息天数`, path: `${i}.interest_days`, type: 'Integer', group: `产品${chineseProductIndex}` });
                    }
                    if (allFields.has('remarks')) {
                        fields.push({ name: `备注`, path: `${i}.remarks`, type: 'String', group: `产品${chineseProductIndex}` });
                    }
                }
            } else if (fieldStructure.name === '宁银费用变更') {
                // 宁银费用变更：动态处理产品信息和费用变更数组

                // 添加基本信息字段
                fields.push(
                    { name: '发行机构', path: 'issuer', type: 'String', group: '基本信息' },
                    { name: '生效日期', path: 'effective_date', type: 'String', group: '基本信息' },
                    { name: '通知日期', path: 'announcement_date', type: 'String', group: '基本信息' }
                );

                // 处理产品信息数组
                const productInfoArray = aiData?.product_info || expectedData?.product_info || [];
                const productInfoLength = Array.isArray(productInfoArray) ? productInfoArray.length : 0;

                for (let i = 0; i < Math.max(productInfoLength, 1); i++) {
                    const chineseIndex = numberToChinese(i + 1);
                    fields.push(
                        { name: '产品名称', path: `product_info.${i}.product_name`, type: 'String', group: `产品信息${chineseIndex}` },
                        { name: '产品代码', path: `product_info.${i}.product_code`, type: 'String', group: `产品信息${chineseIndex}` }
                    );
                }

                // 处理费用变更数组
                const feeChangesArray = aiData?.fee_changes || expectedData?.fee_changes || [];
                const feeChangesLength = Array.isArray(feeChangesArray) ? feeChangesArray.length : 0;

                for (let i = 0; i < Math.max(feeChangesLength, 1); i++) {
                    const chineseIndex = numberToChinese(i + 1);
                    fields.push(
                        { name: '费用类型', path: `fee_changes.${i}.fee_type`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '原始费率', path: `fee_changes.${i}.original_rate`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '新费率', path: `fee_changes.${i}.new_rate`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '生效开始', path: `fee_changes.${i}.effective_start`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '生效结束', path: `fee_changes.${i}.effective_end`, type: 'String', group: `费用变更${chineseIndex}` },
                        { name: '备注', path: `fee_changes.${i}.notes`, type: 'String', group: `费用变更${chineseIndex}` }
                    );
                }
            }
        } catch (error) {
            console.error('动态生成字段时出错:', error);
            // 出错时返回默认字段
            if (fieldStructure.name === '券商账户计息变更') {
                fields.push(
                    { name: '产品名称', path: '0.product_name', type: 'String', group: '产品 一' },
                    { name: '产品类型', path: '0.product_type', type: 'String', group: '产品 一' },
                    { name: '个人利率', path: '0.interest_rate.individual', type: 'String', group: '产品 一' },
                    { name: '非个人利率_START:2024-11-11', path: '0.interest_rate.non_individual.START:2024-11-11', type: 'String', group: '产品 一' },
                    { name: '非个人利率_2024-11-11:END', path: '0.interest_rate.non_individual.2024-11-11:END', type: 'String', group: '产品 一' },
                    { name: '非个人利率_START:2024-11-01', path: '0.interest_rate.non_individual.START:2024-11-01', type: 'String', group: '产品 一' },
                    { name: '利率(年化)', path: '0.interest_rate.all', type: 'String', group: '产品 一' },
                    { name: '开始时间', path: '0.start_time', type: 'Date', group: '产品 一' },
                    { name: '截止时间', path: '0.end_time', type: 'String', group: '产品 一' },
                    { name: '计息天数', path: '0.interest_days', type: 'Integer', group: '产品 一' },
                    { name: '备注', path: '0.remarks', type: 'String', group: '产品 一' }
                );
            } else if (fieldStructure.name === '宁银费用变更') {
                fields.push(
                    { name: '发行机构', path: 'issuer', type: 'String', group: '基本信息' },
                    { name: '生效日期', path: 'effective_date', type: 'String', group: '基本信息' },
                    { name: '通知日期', path: 'announcement_date', type: 'String', group: '基本信息' },
                    { name: '产品名称', path: 'product_info.0.product_name', type: 'String', group: '产品信息一' },
                    { name: '产品代码', path: 'product_info.0.product_code', type: 'String', group: '产品信息一' },
                    { name: '费用类型', path: 'fee_changes.0.fee_type', type: 'String', group: '费用变更一' },
                    { name: '原始费率', path: 'fee_changes.0.original_rate', type: 'String', group: '费用变更一' },
                    { name: '新费率', path: 'fee_changes.0.new_rate', type: 'String', group: '费用变更一' },
                    { name: '生效开始', path: 'fee_changes.0.effective_start', type: 'String', group: '费用变更一' },
                    { name: '生效结束', path: 'fee_changes.0.effective_end', type: 'String', group: '费用变更一' },
                    { name: '备注', path: 'fee_changes.0.notes', type: 'String', group: '费用变更一' }
                );
            } else {
                fields.push({
                    name: '销售机构 1',
                    path: '销售机构.0',
                    type: 'String',
                    group: '销售机构'
                });
            }
        }

        return fields;
    }

    // 渲染包含数组字段的内容（四列合并布局）
    function renderArrayFieldContentMerged(fieldStructure, aiData, expectedData, comparisonResult) {
        const container = document.getElementById('fourColumnsContentContainer');
        if (!container) {
            console.error('找不到fourColumnsContentContainer容器');
            return;
        }
        container.innerHTML = '';

        // 解析JSON数据
        const aiResult = parseJSONData(aiData);
        const expectedResult = parseJSONData(expectedData);

        if (!aiResult && !expectedResult) {
            container.innerHTML = '<div class="text-muted">暂无数据</div>';
            return;
        }

        // 按分组组织字段
        const groups = groupFieldsByGroup(fieldStructure.fields);

        // 渲染每个分组
        Object.keys(groups).forEach(groupName => {
            const groupFields = groups[groupName];

            // 添加分组标题
            addMergedGroupHeader(container, groupName);

            // 处理该分组的字段
            groupFields.forEach(field => {
                if (field.type === 'Array' && field.arrayFields) {
                    // 处理数组字段
                    const aiArrayData = getValueByPath(aiResult, field.path) || [];
                    const expectedArrayData = getValueByPath(expectedResult, field.path) || [];
                    const maxLength = Math.max(aiArrayData.length, expectedArrayData.length);

                    if (maxLength === 0) {
                        // 如果数组为空，显示空数组提示
                        addFourColumnRow(container, {
                            fieldName: field.name,
                            aiValue: '[]',
                            expectedValue: '[]',
                            comparisonResult: getComparisonIcon('[]', '[]'),
                            fieldPath: field.path
                        });
                    } else {
                        // 为每个数组项创建子字段
                        for (let index = 0; index < maxLength; index++) {
                            const aiItem = aiArrayData[index] || {};
                            const expectedItem = expectedArrayData[index] || {};

                            // 为数组项添加标题
                            if (field.arrayFields.length > 1) {
                                // 特殊处理联系人信息，显示为"联系人 1"、"联系人 2"等
                                const displayTitle = field.path === 'contact_info' ?
                                    `联系人 ${index + 1}` : `${field.name} [${index}]`;
                                addArrayItemSubHeader(container, displayTitle);
                            }

                            // 渲染数组项的子字段
                            field.arrayFields.forEach(subField => {
                                const aiValue = getValueByPath(aiItem, subField.path);
                                const expectedValue = getValueByPath(expectedItem, subField.path);
                                // 特殊处理联系人信息的字段名称显示
                                const fieldDisplayName = field.arrayFields.length > 1 ?
                                    `${subField.name}` :
                                    (field.path === 'contact_info' ?
                                        `联系人${index + 1} ${subField.name}` :
                                        `${field.name}[${index}].${subField.name}`);

                                addFourColumnRow(container, {
                                    fieldName: fieldDisplayName,
                                    aiValue: formatFieldValue(aiValue, subField.type, subField.path),
                                    expectedValue: formatFieldValue(expectedValue, subField.type, subField.path),
                                    comparisonResult: getComparisonIcon(aiValue, expectedValue),
                                    fieldPath: `${field.path}[${index}].${subField.path}`
                                });
                            });
                        }
                    }
                } else {
                    // 处理普通字段
                    const fieldPath = field.originalPath || field.originalName || field.path;
                    const aiValue = getValueByPath(aiResult, fieldPath);
                    const expectedValue = getValueByPath(expectedResult, fieldPath);

                    addFourColumnRow(container, {
                        fieldName: field.displayName || field.name,
                        aiValue: formatFieldValue(aiValue, field.type, fieldPath),
                        expectedValue: formatFieldValue(expectedValue, field.type, fieldPath),
                        comparisonResult: getComparisonIcon(aiValue, expectedValue),
                        fieldPath: fieldPath
                    });
                }
            });
        });
    }

    // 添加数组项子标题行
    function addArrayItemSubHeader(container, title) {
        const subHeaderRow = document.createElement('div');
        subHeaderRow.className = 'array-item-sub-header-row';
        subHeaderRow.style.cssText = `
            display: grid;
            grid-template-columns: 1fr;
            background: rgba(108, 117, 125, 0.1);
            border: 1px solid rgba(108, 117, 125, 0.2);
            border-radius: 4px;
            margin: 8px 0 4px 0;
            padding: 8px 12px;
        `;

        const subHeader = document.createElement('div');
        subHeader.className = 'array-item-sub-header';
        subHeader.style.cssText = `
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9rem;
        `;
        subHeader.textContent = title;

        subHeaderRow.appendChild(subHeader);
        container.appendChild(subHeaderRow);
    }

    // 添加数组项标题行
    function addArrayItemHeader(container, itemIndex) {
        const itemRow = document.createElement('div');
        itemRow.className = 'array-item-header-row';
        itemRow.style.cssText = `
            display: grid;
            grid-template-columns: 1fr;
            margin: 15px 0 8px 0;
        `;

        const itemHeader = document.createElement('div');
        itemHeader.className = 'array-item-header';
        itemHeader.textContent = `📋 记录 ${itemIndex}`;
        itemHeader.style.cssText = `
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 10px 15px;
            font-weight: 700;
            font-size: 15px;
            border-radius: 8px;
            box-shadow: 0 3px 6px rgba(0, 184, 148, 0.3);
            border-left: 4px solid #00a085;
            text-align: center;
        `;

        itemRow.appendChild(itemHeader);
        container.appendChild(itemRow);
    }

    // 渲染数组类型的字段内容（五列布局，保留备用）
    function renderArrayFieldContent(fieldStructure, aiData, expectedData, comparisonResult) {
        // 确保数据为数组
        const aiArray = Array.isArray(aiData) ? aiData : [aiData];
        const expectedArray = Array.isArray(expectedData) ? expectedData : [expectedData];
        const maxLength = Math.max(aiArray.length, expectedArray.length);

        // 检查容器是否存在
        const containers = ['fieldNamesContent', 'aiResultContent', 'expectedResultContent', 'comparisonResultContent'];
        for (const containerId of containers) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`找不到容器元素: ${containerId}`);
                return;
            }
        }

        // 渲染字段名称列
        renderFieldNames('fieldNamesContent', fieldStructure.fields);

        // 渲染AI识别结果列
        renderArrayFieldValues('aiResultContent', fieldStructure.fields, aiArray, 'ai-value', false);

        // 渲染预期结果列
        renderArrayFieldValues('expectedResultContent', fieldStructure.fields, expectedArray, 'expected-value', true);

        // 渲染对比结果列
        renderArrayFieldComparison('comparisonResultContent', fieldStructure.fields, aiArray, expectedArray);
    }

    // 渲染数组字段值列
    function renderArrayFieldValues(containerId, fields, dataArray, valueClass, editable = false) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器元素:', containerId);
            return;
        }
        container.innerHTML = '';

        if (!dataArray || dataArray.length === 0) {
            container.innerHTML = '<div class="text-muted">暂无数据</div>';
            return;
        }

        // 为每个数组项创建一个分组
        dataArray.forEach((item, index) => {
            const groups = groupFieldsByGroup(fields);

            // 创建数组项标题
            const itemHeader = document.createElement('div');
            itemHeader.className = 'array-item-header';
            itemHeader.textContent = `联系人 ${index + 1}`;
            container.appendChild(itemHeader);

            // 渲染每个分组
            Object.keys(groups).forEach(groupName => {
                // 创建分组标题
                const groupHeader = document.createElement('div');
                groupHeader.className = 'field-group-header';
                groupHeader.textContent = groupName;
                container.appendChild(groupHeader);

                // 创建分组内容容器
                const groupContent = document.createElement('div');
                groupContent.className = 'field-group-content';

                groups[groupName].forEach(field => {
                    const value = getValueByPath(item, field.path);
                    const displayValue = formatFieldValue(value, field.type, field.path);

                    const fieldRow = document.createElement('div');
                    fieldRow.className = 'field-row';

                    const fieldValue = document.createElement('div');
                    fieldValue.className = `field-value ${valueClass}`;

                    if (editable) {
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.className = 'field-edit-input';
                        input.value = displayValue;
                        input.dataset.field = field.path;
                        input.dataset.arrayIndex = index;
                        fieldValue.appendChild(input);
                    } else {
                        // 检查是否是备注字段
                        const isRemarksField = field.name && (field.name.includes('备注') || field.path.includes('remarks'));

                        if (isRemarksField) {
                            // 备注字段允许换行显示
                            fieldValue.style.whiteSpace = 'pre-wrap';
                            fieldValue.style.wordBreak = 'break-word';
                            fieldValue.style.maxHeight = 'none';
                            fieldValue.style.overflow = 'visible';
                        }

                        fieldValue.textContent = displayValue;
                        fieldValue.title = `原始值: ${value !== null && value !== undefined ? String(value) : '空值'}\n字段类型: ${field.type}\n分组: ${groupName}\n联系人: ${index + 1}`;
                    }

                    fieldRow.appendChild(fieldValue);
                    groupContent.appendChild(fieldRow);
                });

                container.appendChild(groupContent);
            });
        });
    }

    // 渲染数组字段对比结果列
    function renderArrayFieldComparison(containerId, fields, aiArray, expectedArray) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到容器元素:', containerId);
            return;
        }
        container.innerHTML = '';

        const maxLength = Math.max(aiArray.length, expectedArray.length);

        for (let index = 0; index < maxLength; index++) {
            const aiItem = aiArray[index] || {};
            const expectedItem = expectedArray[index] || {};
            const groups = groupFieldsByGroup(fields);

            // 创建数组项标题
            const itemHeader = document.createElement('div');
            itemHeader.className = 'array-item-header';
            itemHeader.textContent = `联系人 ${index + 1}`;
            container.appendChild(itemHeader);

            // 渲染每个分组
            Object.keys(groups).forEach(groupName => {
                // 创建分组标题
                const groupHeader = document.createElement('div');
                groupHeader.className = 'field-group-header';
                groupHeader.textContent = groupName;
                container.appendChild(groupHeader);

                // 创建分组内容容器
                const groupContent = document.createElement('div');
                groupContent.className = 'field-group-content';

                groups[groupName].forEach(field => {
                    const aiValue = getValueByPath(aiItem, field.path);
                    const expectedValue = getValueByPath(expectedItem, field.path);
                    const isMatch = compareFieldValues(aiValue, expectedValue);

                    const fieldRow = document.createElement('div');
                    fieldRow.className = 'field-row';

                    const comparisonStatus = document.createElement('div');
                    comparisonStatus.className = `comparison-status ${isMatch ? 'match' : 'mismatch'}`;
                    comparisonStatus.innerHTML = `
                        <i class="bi bi-${isMatch ? 'check-circle' : 'x-circle'}"></i>
                        ${isMatch ? '匹配' : '不匹配'}
                    `;

                    const aiStr = formatFieldValue(aiValue, field.type, field.path);
                    const expectedStr = formatFieldValue(expectedValue, field.type, field.path);
                    comparisonStatus.title = `AI值: ${aiStr}\n预期值: ${expectedStr}\n字段: ${field.name}\n联系人: ${index + 1}`;

                    fieldRow.appendChild(comparisonStatus);
                    groupContent.appendChild(fieldRow);
                });

                container.appendChild(groupContent);
            });
        }
    }

    // 保持原有的JSON渲染函数作为备用
    function renderJSONContent(containerId, jsonData, editable = false) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (!jsonData) {
            container.innerHTML = '<div class="text-muted">暂无数据</div>';
            return;
        }

        Object.keys(jsonData).forEach(key => {
            const field = document.createElement('div');
            field.className = 'json-field';

            const fieldName = document.createElement('div');
            fieldName.className = 'json-field-name';
            fieldName.textContent = key;

            const fieldValue = document.createElement('div');
            fieldValue.className = 'json-field-value';

            if (editable) {
                const input = document.createElement('textarea');
                input.className = 'editable-field';
                input.value = typeof jsonData[key] === 'object' ? JSON.stringify(jsonData[key], null, 2) : jsonData[key];
                input.dataset.fieldKey = key;
                fieldValue.appendChild(input);
            } else {
                // 检查是否是备注字段
                const isRemarksField = key.includes('备注') || key.includes('remarks');

                if (isRemarksField) {
                    // 备注字段允许换行显示
                    fieldValue.style.whiteSpace = 'pre-wrap';
                    fieldValue.style.wordBreak = 'break-word';
                    fieldValue.style.maxHeight = 'none';
                    fieldValue.style.overflow = 'visible';
                }

                fieldValue.textContent = typeof jsonData[key] === 'object' ? JSON.stringify(jsonData[key], null, 2) : jsonData[key];
            }

            field.appendChild(fieldName);
            field.appendChild(fieldValue);
            container.appendChild(field);
        });
    }

    // 渲染对比结果
    function renderComparisonResult(containerId, comparisonData) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (!comparisonData) {
            container.innerHTML = '<div class="text-muted">暂无对比数据</div>';
            return;
        }

        if (comparisonData.field_comparison) {
            Object.keys(comparisonData.field_comparison).forEach(key => {
                const comparison = comparisonData.field_comparison[key];
                const field = document.createElement('div');
                field.className = 'json-field';

                const fieldName = document.createElement('div');
                fieldName.className = 'json-field-name';
                fieldName.innerHTML = `${key} <span class="comparison-indicator ${comparison.match ? 'comparison-match' : 'comparison-diff'}">
                    <i class="bi bi-${comparison.match ? 'check' : 'x'}"></i>
                    ${comparison.match ? '匹配' : '不匹配'}
                </span>`;

                const fieldValue = document.createElement('div');
                fieldValue.className = 'json-field-value';
                fieldValue.innerHTML = `
                    <div><strong>AI值:</strong> ${comparison.ai_value}</div>
                    <div><strong>预期值:</strong> ${comparison.expected_value}</div>
                `;

                field.appendChild(fieldName);
                field.appendChild(fieldValue);
                container.appendChild(field);
            });
        }

        // 显示整体准确率
        if (comparisonData.accuracy_score !== undefined) {
            const accuracyField = document.createElement('div');
            accuracyField.className = 'json-field';
            accuracyField.innerHTML = `
                <div class="json-field-name">整体准确率</div>
                <div class="json-field-value">
                    <strong style="color: #FFD700;">${(comparisonData.accuracy_score * 100).toFixed(1)}%</strong>
                </div>
            `;
            container.appendChild(accuracyField);
        }
    }

    // 根据路径设置嵌套对象的值
    function setValueByPath(obj, path, value) {
        const keys = path.split('.');
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current)) {
                // 检查下一个键是否是数组索引
                const nextKey = keys[i + 1];
                current[key] = !isNaN(nextKey) ? [] : {};
            }
            current = current[key];
        }

        const lastKey = keys[keys.length - 1];
        if (!isNaN(lastKey) && Array.isArray(current)) {
            current[parseInt(lastKey)] = value;
        } else {
            current[lastKey] = value;
        }
    }

    // 测试字段渲染功能
    function testFieldRendering() {
        // 创建测试数据 - 模拟账户开户场景V1.2的字段结构
        const testFields = [
            { name: '管理机构名称', path: 'manager_info.name', type: 'String', group: '管理机构信息' },
            { name: '管理机构地址', path: 'manager_info.address', type: 'String', group: '管理机构信息' },
            { name: '投资者名称', path: 'investor_info.name', type: 'String', group: '投资者信息' },
            { name: '账户性质', path: 'investor_info.account_nature', type: 'String', group: '投资者信息' },
            { name: '联系人信息', path: 'contact_info', type: 'Array', group: '联系人信息' }
        ];
        const groups = groupFieldsByGroup(testFields);
        // 验证分组结果
        Object.keys(groups).forEach(groupName => {
            groups[groupName].forEach(field => {
            });
        });
    }

    // 保存预期结果
    function saveExpectedResult() {
        const editableFields = document.querySelectorAll('#expectedResultContent .editable-field');
        const expectedResult = {};

        editableFields.forEach(field => {
            const path = field.dataset.fieldPath || field.dataset.fieldKey;
            let value = field.value.trim();

            // 尝试解析JSON
            try {
                value = JSON.parse(value);
            } catch (e) {
                // 如果不是JSON，保持字符串
            }

            if (path) {
                if (path.includes('.')) {
                    // 使用路径设置嵌套值
                    setValueByPath(expectedResult, path, value);
                } else {
                    // 直接设置顶级属性
                    expectedResult[path] = value;
                }
            }
        });

        showLoading('正在保存预期结果...');

        fetch(`/api/files/${currentRecordId}/expected-result`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ expected_result: expectedResult })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage('预期结果保存成功', 'success');
                // 重新加载对比结果
                viewResult(currentRecordId);
            } else {
                showMessage('保存预期结果失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('保存预期结果失败:', error);
            showMessage('保存预期结果失败', 'error');
        });
    }

    // 审核文件
    function auditFile(result) {
        const comment = document.getElementById('auditComment').value.trim();
        
        showLoading('正在提交审核结果...');

        fetch(`/api/files/${currentRecordId}/audit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                audit_status: result,
                audit_comment: comment
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage(`审核${result === 'pass' ? '通过' : '不通过'}`, 'success');
                
                // 如果开启了自动切换，关闭当前模态框并打开下一个
                if (autoNextEnabled) {
                    closeModalAndNext();
                } else {
                    // 刷新文件列表
                    loadFileList(currentPage);
                }
            } else {
                showMessage('审核失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('审核失败:', error);
            showMessage('审核失败', 'error');
        });
    }

    // 自动切换下一个开关
    function toggleAutoNext() {
        const switchElement = document.getElementById('autoNextSwitch');
        autoNextEnabled = switchElement.checked;
        // 保存用户偏好到本地存储
        localStorage.setItem('autoNextEnabled', autoNextEnabled);
    }

    // 手动切换到下一个文件
    function goToNextFile() {
        // 关闭当前弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
        if (modal) {
            modal.hide();
        }

        // 基于当前列表查找下一个文件
        setTimeout(() => {
            findNextFileInCurrentList();
        }, 300);
    }

    // 手动切换到上一个文件
    function goToPreviousFile() {
        // 关闭当前弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
        if (modal) {
            modal.hide();
        }

        // 基于当前列表查找上一个文件
        setTimeout(() => {
            findPreviousFileInCurrentList();
        }, 300);
    }

    // 关闭模态框并切换到下一个
    function closeModalAndNext() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('resultModal'));
        if (modal) {
            modal.hide();
        }

        if (autoNextEnabled) {
            // 查找下一个文件（基于当前列表）
            setTimeout(() => {
                findNextFileInCurrentList();
            }, 300);
        }
    }

    // 基于当前列表查找下一个文件
    function findNextFileInCurrentList() {
        if (!currentFileList || currentFileList.length === 0) {
            findNextReviewFile();
            return;
        }

        // 查找当前文件在列表中的索引
        const currentIndex = currentFileList.findIndex(file => file.id === currentRecordId);
        if (currentIndex === -1) {
            findNextReviewFile();
            return;
        }

        // 查找下一个文件
        let nextFile = null;
        for (let i = currentIndex + 1; i < currentFileList.length; i++) {
            const file = currentFileList[i];
            // 查找有结果的文件（已完成分析的文件）
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                nextFile = file;
                break;
            }
        }

        if (nextFile) {
            showLoading('正在切换到下一个文件...');
            setTimeout(() => {
                hideLoading();
                viewResult(nextFile.id);
            }, 300);
        } else {
            // 当前页没有下一个文件，检查是否有下一页
            if (currentPage < totalPages) {
                showLoading('正在加载下一页...');
                loadFileList(currentPage + 1);
                setTimeout(() => {
                    hideLoading();
                    // 加载下一页后，查找第一个有结果的文件
                    findFirstFileWithResult();
                }, 1000);
            } else {
                showMessage('已经是最后一个文件了，没有更多文件可查看', 'warning');
                loadFileList(currentPage);
            }
        }
    }

    // 基于当前列表查找上一个文件
    function findPreviousFileInCurrentList() {
        if (!currentFileList || currentFileList.length === 0) {
            findPreviousReviewFile();
            return;
        }

        // 查找当前文件在列表中的索引
        const currentIndex = currentFileList.findIndex(file => file.id === currentRecordId);
        if (currentIndex === -1) {
            findPreviousReviewFile();
            return;
        }

        // 查找上一个文件
        let previousFile = null;
        for (let i = currentIndex - 1; i >= 0; i--) {
            const file = currentFileList[i];
            // 查找有结果的文件（已完成分析的文件）
            if (['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active') {
                previousFile = file;
                break;
            }
        }

        if (previousFile) {
            showLoading('正在切换到上一个文件...');
            setTimeout(() => {
                hideLoading();
                viewResult(previousFile.id);
            }, 300);
        } else {
            // 当前页没有上一个文件，检查是否有上一页
            if (currentPage > 1) {
                showLoading('正在加载上一页...');
                loadFileList(currentPage - 1);
                setTimeout(() => {
                    hideLoading();
                    // 加载上一页后，查找最后一个有结果的文件
                    findLastFileWithResult();
                }, 1000);
            } else {
                showMessage('已经是第一个文件了，没有更多文件可查看', 'warning');
                loadFileList(currentPage);
            }
        }
    }

    // 查找最后一个有结果的文件
    function findLastFileWithResult() {
        if (!currentFileList || currentFileList.length === 0) {
            showMessage('没有找到可查看的文件', 'warning');
            return;
        }

        const lastFileWithResult = currentFileList.findLast(file =>
            ['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active'
        );

        if (lastFileWithResult) {
            viewResult(lastFileWithResult.id);
        } else {
            showMessage('当前页没有可查看的文件，没有更多文件了', 'warning');
        }
    }

    // 查找第一个有结果的文件
    function findFirstFileWithResult() {
        if (!currentFileList || currentFileList.length === 0) {
            showMessage('没有找到可查看的文件', 'warning');
            return;
        }

        const firstFileWithResult = currentFileList.find(file =>
            ['completed', 'pending_audit', 'pending_review'].includes(file.status) && file.file_status === 'active'
        );

        if (firstFileWithResult) {
            viewResult(firstFileWithResult.id);
        } else {
            showMessage('当前页没有可查看的文件，没有更多文件了', 'warning');
        }
    }

    // 查找下一个待审核文件（原有逻辑，作为备用）
    function findNextReviewFile() {
        showLoading('正在查找下一个文件...');

        fetch('/api/files/next-pending')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                hideLoading();

                if (data.success && data.data) {
                    // 延迟一下再打开下一个文件，确保弹窗完全关闭
                    setTimeout(() => {
                        viewResult(data.data.id);
                    }, 500);
                } else {
                    showMessage('没有更多待审核的文件了，已经是最后一个文件', 'warning');
                    // 刷新文件列表
                    loadFileList(currentPage);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('查找下一个待审核文件失败:', error);
                showMessage('查找下一个文件失败: ' + error.message, 'error');
            });
    }

    // 查找上一个待审核文件（原有逻辑，作为备用）
    function findPreviousReviewFile() {
        showLoading('正在查找上一个文件...');

        fetch('/api/files/previous-pending')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                hideLoading();

                if (data.success && data.data) {
                    // 延迟一下再打开上一个文件，确保弹窗完全关闭
                    setTimeout(() => {
                        viewResult(data.data.id);
                    }, 500);
                } else {
                    showMessage('没有更多待审核的文件了，已经是第一个文件', 'warning');
                    // 刷新文件列表
                    loadFileList(currentPage);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('查找上一个待审核文件失败:', error);
                showMessage('查找上一个文件失败: ' + error.message, 'error');
            });
    }

    // 加载和隐藏遮罩层
    function showLoading(text = '正在处理...') {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    function hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // 消息提示
    function showMessage(message, type = 'info') {
        // 创建Toast提示
        const toastContainer = getOrCreateToastContainer();
        const toast = createToast(message, type);
        toastContainer.appendChild(toast);

        // 显示Toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // 自动移除Toast元素
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    // 获取或创建Toast容器
    function getOrCreateToastContainer() {
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    // 创建Toast元素
    function createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const typeConfig = {
            'success': { icon: 'bi-check-circle-fill', color: 'text-success', title: '成功' },
            'error': { icon: 'bi-exclamation-triangle-fill', color: 'text-danger', title: '错误' },
            'warning': { icon: 'bi-exclamation-triangle-fill', color: 'text-warning', title: '警告' },
            'info': { icon: 'bi-info-circle-fill', color: 'text-info', title: '信息' }
        };

        const config = typeConfig[type] || typeConfig['info'];

        toast.innerHTML = `
            <div class="toast-header">
                <i class="bi ${config.icon} ${config.color} me-2"></i>
                <strong class="me-auto">${config.title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="关闭"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        return toast;
    }

    // 测试弹窗功能（使用真实数据格式）
    function testModalWithRealData() {
        try {
            // 模拟真实的API返回数据
            const testData = {
                filename: '期货账户开户申请表.pdf',
                analysis_type: 'futures_account',
                accuracy_score: 1.0,
                field_accuracy: 0.857,
                total_fields: 7,
                correct_fields: 6,
                comparison_result: {
                    total_fields: 7,
                    matched_fields: 6,
                    accuracy_score: 0.857,
                    field_comparisons: [
                        { field_name: '产品名称', ai_value: '合规智能期货账户开户业务委托', expected_value: '合规智能期货账户开户业务委托', match: true },
                        { field_name: '资金账号', ai_value: '********', expected_value: '********', match: true },
                        { field_name: '会员号_上期所', ai_value: '0249', expected_value: '0249', match: true },
                        { field_name: '会员号_大商所', ai_value: '0101', expected_value: '0101', match: true },
                        { field_name: '会员号_郑商所', ai_value: '020b', expected_value: '020b', match: false },
                        { field_name: '会员号_中金所', ai_value: '0316', expected_value: '0316', match: true },
                        { field_name: '开始时间', ai_value: '2024-01-15', expected_value: '2024-01-15', match: true }
                    ]
                },
                ai_result: {
                    产品名称: '合规智能期货账户开户业务委托',
                    资金账号: '********',
                    会员号: {
                        上期所: '0249',
                        大商所: '0101',
                        郑商所: '020b',
                        中金所: '0316'
                    },
                    开始时间: '2024-01-15'
                },
                expected_result: {
                    产品名称: '合规智能期货账户开户业务委托',
                    资金账号: '********',
                    会员号: {
                        上期所: '0249',
                        大商所: '0101',
                        郑商所: '020b',
                        中金所: '0316'
                    },
                    开始时间: '2024-01-15'
                },
                audit_comment: '测试数据，识别准确'
            };
            showResultModal(testData);
        } catch (error) {
            console.error('测试弹窗功能时出错:', error);
            showMessage('测试弹窗功能失败: ' + error.message, 'error');
        }
    }

    // 测试异常数据的弹窗功能
    function testModalWithBadData() {
        try {
            // 模拟异常的API返回数据
            const testData = {
                filename: '异常测试文件.pdf',
                analysis_type: null,
                accuracy_score: null,
                field_accuracy: NaN,
                comparison_result: {},
                audit_comment: ''
            };
            showResultModal(testData);
        } catch (error) {
            console.error('测试异常数据弹窗功能时出错:', error);
            showMessage('测试异常数据弹窗功能失败: ' + error.message, 'error');
        }
    }

    // 简单的四个指标测试函数
    function testStatsOnly() {
        try {
            // 只测试统计信息更新
            const testData = {
                filename: '统计测试文件.pdf',
                accuracy_score: 0.95,
                field_accuracy: 0.857,
                comparison_result: {
                    total_fields: 7,
                    matched_fields: 6,
                    accuracy_score: 0.857
                }
            };
            // 直接调用统计信息更新函数
            updateStatsSection(testData);

            showMessage('四个指标测试完成，请查看控制台输出', 'success');
        } catch (error) {
            console.error('测试四个指标时出错:', error);
            showMessage('测试四个指标失败: ' + error.message, 'error');
        }
    }

    // 测试功能修复的函数 - 与文档分析页面保持一致
    window.testFixes = function() {
        console.log('🔧 测试功能修复（文件管理页面）');

        // 1. 测试时间格式化
        const testDate = new Date();
        const formattedTime = formatDateTime(testDate);
        console.log('✅ 时间格式化测试:', formattedTime);

        // 2. 测试准确率处理
        const testAccuracy1 = 0.95; // 小数形式
        const testAccuracy2 = 95.5; // 百分比形式
        console.log('✅ 准确率处理测试 (小数):', testAccuracy1 > 1 ? testAccuracy1.toFixed(1) : (testAccuracy1 * 100).toFixed(1));
        console.log('✅ 准确率处理测试 (百分比):', testAccuracy2 > 1 ? testAccuracy2.toFixed(1) : (testAccuracy2 * 100).toFixed(1));

        // 3. 测试字段计数功能
        const testAiResult = {
            "manager_info": {"name": "测试机构", "address": "测试地址", "contact": "测试联系方式"},
            "investor_info": {"name": "测试投资者", "type": "个人", "account_nature": "普通"},
            "contact_info": [{"contact_person": "张三", "phone": "***********"}],
            "seal_integrity": "complete",
            "page_continuity": "continuous"
        };
        const fieldCount = countFieldsInObject(testAiResult);
        console.log('✅ 字段计数测试:', fieldCount, '个字段');

        // 4. 测试统计信息更新
        const mockResultData = {
            analysis_type: 'account_opening',
            ai_result: testAiResult,
            expected_result: null, // 模拟没有预期结果的情况
            accuracy_score: null
        };
        console.log('✅ 测试统计信息更新（无预期结果）');
        updateStatsSection(mockResultData);

        // 5. 测试文件列表刷新
        loadFileList(currentPage);
        console.log('✅ 文件列表刷新完成');

        console.log('🎉 所有测试完成');
    }

    // 简单的API测试函数 - 与文档分析页面保持一致
    window.testAPI = function() {
        console.log('🧪 测试API连接（文件管理页面）');
        fetch('/api/files', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('🔍 API响应状态:', response.status);
            console.log('🔍 API响应头:', response.headers);
            return response.json();
        })
        .then(data => {
            console.log('📊 API响应数据:', data);
        })
        .catch(error => {
            console.error('❌ API请求失败:', error);
        });
    }



    // 全屏查看功能
    function toggleFullscreen() {
        const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
        const fullscreenContent = document.getElementById('fullscreenContent');
        const originalContent = document.getElementById('originalFileContent');

        // 复制原件内容到全屏模态框
        const fileElement = originalContent.querySelector('iframe, img');
        if (fileElement) {
            const clonedElement = fileElement.cloneNode(true);
            clonedElement.style.width = '100%';
            clonedElement.style.height = '100%';
            clonedElement.style.border = 'none';

            fullscreenContent.innerHTML = '';
            fullscreenContent.appendChild(clonedElement);

            modal.show();
        } else {
            showMessage('没有可全屏查看的内容', 'warning');
        }
    }

    // 监听全屏模态框关闭事件 - 移到initEventListeners中

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initEventListeners();
        loadFileList(1);
    });
</script>
 
 <!-- 全屏查看模态框 -->
 <div class="modal fade" id="fullscreenModal" tabindex="-1" aria-labelledby="fullscreenModalLabel" aria-hidden="true">
     <div class="modal-dialog modal-fullscreen">
         <div class="modal-content">
             <div class="modal-header bg-dark text-white">
                 <h5 class="modal-title" id="fullscreenModalLabel" style="color: white; font-weight: 500;">
                     <i class="bi bi-file-earmark-image me-2"></i>原件全屏查看
                 </h5>
                 <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
             </div>
             <div class="modal-body p-0" style="position: relative; overflow: hidden;">
                 <div id="fullscreenContent" style="width: 100%; height: calc(100vh - 60px); display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
                     <!-- 全屏内容将在这里显示 -->
                     <div class="text-center text-muted">
                         <i class="bi bi-file-earmark-image" style="font-size: 5rem; opacity: 0.3;"></i>
                         <p class="mt-3">准备全屏显示...</p>
                     </div>
                 </div>


             </div>
         </div>
     </div>
 </div>

 <!-- 加载遮罩层 -->
 <div class="loading-overlay" id="loadingOverlay" style="display: none;">
     <div class="text-center">
         <div class="spinner-border loading-spinner" role="status">
             <span class="visually-hidden">处理中...</span>
         </div>
         <div class="mt-3 text-white">
             <span id="loadingText">正在处理...</span>
         </div>
     </div>
 </div>

<!-- 全局文件选择面板 -->
<div id="globalFileSelectorPanel" style="display: none; position: fixed; z-index: 9999; background: white; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 8px 16px rgba(0,0,0,0.15); min-width: 280px; max-height: 400px; overflow-y: auto;">
    <div class="p-3">
        <div class="fw-bold text-primary mb-3 d-flex align-items-center">
            <i class="bi bi-files me-2"></i>选择要查看的文件
            <button type="button" class="btn-close ms-auto" onclick="hideFileSelector()" aria-label="Close"></button>
        </div>
        <div id="globalFileSelectList">
            <!-- 文件列表将在这里动态生成 -->
        </div>
    </div>
</div>

 {% endblock %}